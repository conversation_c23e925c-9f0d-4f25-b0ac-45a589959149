<template>
  <view class="charts-container">
    <!-- 优雅的页面头部 -->
    <view class="charts-header">
      <view class="header-gradient"></view>
      <view class="header-content">
        <text class="header-title">📊 数据分析中心</text>
        <text class="header-subtitle">智能辐射监测 · 实时数据可视化</text>
        </view>
      
      <!-- 现代化时间范围选择器 -->
      <view class="time-range-container">
        <view class="range-selector">
          <view class="range-option" 
            :class="{ active: selectedTimeRange === '1h' }" 
            @tap="selectTimeRange('1h')">
            <text class="range-text">1小时</text>
            <view class="active-indicator"></view>
      </view>
          <view class="range-option" 
            :class="{ active: selectedTimeRange === '24h' }" 
            @tap="selectTimeRange('24h')">
            <text class="range-text">24小时</text>
            <view class="active-indicator"></view>
    </view>
          <view class="range-option" 
            :class="{ active: selectedTimeRange === '7d' }" 
            @tap="selectTimeRange('7d')">
            <text class="range-text">7天</text>
            <view class="active-indicator"></view>
        </view>
      </view>
        </view>
      </view>
      
    <!-- 炫酷的数据概览卡片 -->
    <view class="overview-section">
      <view class="overview-card primary" @tap="focusOnDoseRate">
        <view class="card-bg-effect"></view>
        <view class="card-content">
          <view class="metric-icon">
            <text class="icon-text">☢️</text>
            <view class="icon-glow"></view>
        </view>
          <view class="metric-data">
            <text class="metric-label">实时剂量率</text>
            <text class="metric-value">{{ radiationState.currentData.doseRate.toFixed(3) }}</text>
            <text class="metric-unit">μSv/h</text>
      </view>
          <view class="trend-container" :class="doseRateTrend.class">
            <view class="trend-arrow">{{ doseRateTrend.icon }}</view>
            <text class="trend-label">{{ doseRateTrend.text }}</text>
          </view>
        </view>
        <view class="card-shine"></view>
    </view>

      <view class="overview-card secondary" @tap="focusOnCumulative">
        <view class="card-bg-effect"></view>
        <view class="card-content">
          <view class="metric-icon">
            <text class="icon-text">📈</text>
            <view class="icon-glow"></view>
      </view>
          <view class="metric-data">
            <text class="metric-label">累积剂量</text>
            <text class="metric-value">{{ radiationState.currentData.doseSum.toFixed(6) }}</text>
            <text class="metric-unit">μSv</text>
      </view>
          <view class="trend-container up">
            <view class="trend-arrow">📊</view>
            <text class="trend-label">持续累积</text>
      </view>
        </view>
        <view class="card-shine"></view>
    </view>

      <view class="overview-card tertiary" @tap="focusOnCPS">
        <view class="card-bg-effect"></view>
        <view class="card-content">
          <view class="metric-icon">
            <text class="icon-text">⚡</text>
            <view class="icon-glow"></view>
          </view>
          <view class="metric-data">
            <text class="metric-label">计数率</text>
            <text class="metric-value">{{ radiationState.currentData.cps.toFixed(1) }}</text>
            <text class="metric-unit">CPS</text>
        </view>
          <view class="trend-container" :class="cpsTrend.class">
            <view class="trend-arrow">{{ cpsTrend.icon }}</view>
            <text class="trend-label">{{ cpsTrend.text }}</text>
        </view>
          </view>
        <view class="card-shine"></view>
        </view>
      </view>

    <!-- 超炫酷的主图表区域 -->
    <view class="main-chart-section">
      <view class="chart-card">
        <view class="chart-header">
          <view class="chart-title-container">
            <text class="chart-title">📈 实时数据趋势分析</text>
            <text class="chart-subtitle">智能可视化 · 趋势预测</text>
        </view>
          <view class="chart-controls">
            <view class="control-btn refresh" @tap="refreshChart">
              <text class="btn-icon">🔄</text>
              <view class="btn-ripple"></view>
        </view>
            <view class="control-btn export" @tap="exportChart">
              <text class="btn-icon">📤</text>
              <view class="btn-ripple"></view>
      </view>
            <view class="control-btn settings" @tap="openChartSettings">
              <text class="btn-icon">⚙️</text>
              <view class="btn-ripple"></view>
    </view>
          </view>
        </view>
        
        <view class="chart-container">
          <view class="chart-wrapper">
            <canvas canvas-id="mainChart" class="chart-canvas" :style="{ width: chartWidth + 'px', height: chartHeight + 'px' }"></canvas>
            <view class="chart-overlay" v-if="isLoading">
              <view class="loading-animation">
                <view class="loading-dots">
                  <view class="dot"></view>
                  <view class="dot"></view>
                  <view class="dot"></view>
        </view>
                <text class="loading-text">正在分析数据...</text>
      </view>
        </view>
            </view>
          
          <!-- 美化的图表图例 -->
          <view class="enhanced-legend">
            <view class="legend-item dose-rate">
              <view class="legend-indicator">
                <view class="legend-dot"></view>
                <view class="legend-line"></view>
            </view>
              <view class="legend-content">
                <text class="legend-label">剂量率</text>
                <text class="legend-unit">μSv/h</text>
          </view>
      </view>
            <view class="legend-item count-rate">
              <view class="legend-indicator">
                <view class="legend-dot"></view>
                <view class="legend-line"></view>
    </view>
              <view class="legend-content">
                <text class="legend-label">计数率</text>
                <text class="legend-unit">CPS ÷ 10</text>
            </view>
            </view>
            </view>
            </view>
          </view>
        </view>

    <!-- 精美的统计数据网格 -->
    <view class="stats-section">
      <view class="section-header">
        <view class="section-title">
          <text class="section-icon">📊</text>
          <text class="section-text">智能统计分析</text>
            </view>
        <view class="section-action" @tap="viewDetailedStats">
          <text class="action-text">详细报告</text>
          <text class="action-arrow">→</text>
            </view>
            </view>
      
      <view class="stats-grid">
        <view class="stat-card">
          <view class="stat-header">
            <text class="stat-icon">📈</text>
            <text class="stat-title">最大值</text>
            </view>
          <view class="stat-content">
            <text class="stat-value">{{ maxDoseRate.toFixed(3) }}</text>
            <text class="stat-unit">μSv/h</text>
          </view>
          <view class="stat-progress">
            <view class="progress-bar" :style="{ width: maxDoseProgress + '%' }"></view>
        </view>
      </view>

        <view class="stat-card">
          <view class="stat-header">
            <text class="stat-icon">📉</text>
            <text class="stat-title">最小值</text>
        </view>
          <view class="stat-content">
            <text class="stat-value">{{ minDoseRate.toFixed(3) }}</text>
            <text class="stat-unit">μSv/h</text>
          </view>
          <view class="stat-progress">
            <view class="progress-bar" :style="{ width: minDoseProgress + '%' }"></view>
        </view>
      </view>

        <view class="stat-card">
          <view class="stat-header">
            <text class="stat-icon">📊</text>
            <text class="stat-title">平均值</text>
        </view>
          <view class="stat-content">
            <text class="stat-value">{{ avgDoseRate.toFixed(3) }}</text>
            <text class="stat-unit">μSv/h</text>
        </view>
          <view class="stat-progress">
            <view class="progress-bar" :style="{ width: avgDoseProgress + '%' }"></view>
      </view>
    </view>

        <view class="stat-card">
          <view class="stat-header">
            <text class="stat-icon">🎯</text>
            <text class="stat-title">变化率</text>
        </view>
          <view class="stat-content">
            <text class="stat-value">{{ changeRate.toFixed(1) }}</text>
            <text class="stat-unit">%</text>
          </view>
          <view class="stat-progress">
            <view class="progress-bar change" :style="{ width: Math.abs(changeRate) + '%' }"></view>
        </view>
        </view>
      </view>
    </view>

    <!-- 浮动操作按钮 -->
    <view class="fab-container">
      <view class="fab main" @tap="toggleFabMenu">
        <text class="fab-icon">{{ fabMenuOpen ? '✕' : '🔧' }}</text>
        <view class="fab-ripple"></view>
      </view>
      <view class="fab-menu" :class="{ open: fabMenuOpen }">
        <view class="fab-item" @tap="exportData">
          <text class="fab-text">导出数据</text>
          <text class="fab-icon">📊</text>
    </view>
        <view class="fab-item" @tap="shareChart">
          <text class="fab-text">分享图表</text>
          <text class="fab-icon">📤</text>
      </view>
        <view class="fab-item" @tap="resetChart">
          <text class="fab-text">重置视图</text>
          <text class="fab-icon">🔄</text>
      </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState } from '../../utils/dataStore.js'

export default {
  name: 'Charts',
  setup() {
    const selectedTimeRange = ref('24h')
    const isLoading = ref(false)
    const chartWidth = ref(300)
    const chartHeight = ref(300)  // 增加图表高度以匹配CSS
    const fabMenuOpen = ref(false)
    let chartContext = null
    let updateInterval = null

    const doseRateTrend = computed(() => {
      const history = radiationState.history.slice(0, 10)
      if (history.length < 2) return { icon: '➖', text: '无数据', class: 'neutral' }
      
      const current = history[0].doseRate
      const previous = history[1].doseRate
      const change = ((current - previous) / previous * 100)
      
      if (Math.abs(change) < 1) return { icon: '➖', text: '稳定', class: 'stable' }
      if (change > 0) return { icon: '📈', text: `+${change.toFixed(1)}%`, class: 'up' }
      return { icon: '📉', text: `${change.toFixed(1)}%`, class: 'down' }
    })

    const cpsTrend = computed(() => {
      const history = radiationState.history.slice(0, 10)
      if (history.length < 2) return { icon: '➖', text: '无数据', class: 'neutral' }
      
      const current = history[0].cps
      const previous = history[1].cps
      const change = ((current - previous) / previous * 100)
      
      if (Math.abs(change) < 1) return { icon: '➖', text: '稳定', class: 'stable' }
      if (change > 0) return { icon: '📈', text: `+${change.toFixed(1)}%`, class: 'up' }
      return { icon: '📉', text: `${change.toFixed(1)}%`, class: 'down' }
    })

    const todayStats = computed(() => {
      const today = new Date().toDateString()
      const todayData = radiationState.history.filter(item => 
        new Date(item.timestamp).toDateString() === today
      )

      if (todayData.length === 0) {
        return { avgDoseRate: 0, maxDoseRate: 0, totalDose: 0 }
      }

      const doseRates = todayData.map(item => item.doseRate)
      return {
        avgDoseRate: doseRates.reduce((sum, rate) => sum + rate, 0) / doseRates.length,
        maxDoseRate: Math.max(...doseRates),
        totalDose: radiationState.currentData.doseSum
      }
    })

    const todayAlerts = computed(() => {
      const today = new Date().toDateString()
      return radiationState.alerts.filter(alert => 
        new Date(alert.timestamp).toDateString() === today
      )
    })

    const recentHistory = computed(() => {
      return radiationState.history.slice(0, 10)
    })

    // 方法
    const selectTimeRange = (range) => {
      selectedTimeRange.value = range
      drawChart()
    }

    const refreshChart = () => {
      isLoading.value = true
      setTimeout(() => {
        drawChart()
        isLoading.value = false
      }, 1000)
    }

    const exportChart = () => {
      uni.canvasToTempFilePath({
        canvasId: 'mainChart',
        success: (res) => {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
      uni.showToast({
                title: '图表已保存',
        icon: 'success'
      })
    }
          })
        }
      })
    }

    const exportData = () => {
      uni.showActionSheet({
        itemList: ['导出CSV数据', '导出JSON数据', '保存图表图片'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              exportCSV()
              break
            case 1:
              exportJSON()
              break
            case 2:
              exportChart()
              break
          }
        }
      })
    }

    const exportCSV = () => {
      const data = radiationState.history.slice(0, 100)
      let csvContent = '时间,剂量率(μSv/h),计数率(CPS),温度(°C)\n'
      
      data.forEach(item => {
        const time = new Date(item.timestamp).toLocaleString()
        csvContent += `${time},${item.doseRate},${item.cps},${item.temperature}\n`
      })
      
              uni.showToast({
        title: 'CSV数据已准备',
                icon: 'success'
              })
            }

    const exportJSON = () => {
      const data = {
        exportTime: new Date().toISOString(),
        totalRecords: radiationState.history.length,
        data: radiationState.history.slice(0, 100)
      }
      
              uni.showToast({
        title: 'JSON数据已准备',
                icon: 'success'
              })
    }

    const getAlertCount = (type) => {
      return todayAlerts.value.filter(alert => alert.type === type).length
    }

    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const getStatusClass = (doseRate) => {
      if (doseRate > 1.0) return 'high'
      if (doseRate > 0.5) return 'warning'
      return 'normal'
    }

    const getStatusText = (doseRate) => {
      if (doseRate > 1.0) return '高'
      if (doseRate > 0.5) return '中'
      return '正常'
    }

    const initChart = () => {
      chartContext = uni.createCanvasContext('mainChart')
      if (chartContext) {
        drawChart()
        // 设置定时更新
        updateInterval = setInterval(drawChart, 5000)
      }
    }

    const drawChart = () => {
      if (!chartContext) return
      
      const ctx = chartContext
      const width = chartWidth.value
      const height = chartHeight.value
      const padding = 40
      
      // 清空画布
      ctx.clearRect(0, 0, width, height)
      
      // 设置画布背景渐变
      const bgGradient = ctx.createLinearGradient(0, 0, 0, height)
      bgGradient.addColorStop(0, '#fefefe')
      bgGradient.addColorStop(1, '#f8fafc')
      ctx.setFillStyle(bgGradient)
      ctx.fillRect(0, 0, width, height)
      
      // 获取数据 - 扩大显示范围
      let dataCount = 50  // 增加默认数据点
      switch (selectedTimeRange.value) {
        case '1h': dataCount = 30; break    // 增加数据点
        case '24h': dataCount = 60; break   // 增加数据点
        case '7d': dataCount = 168; break   // 7天每小时一个点
      }
      
      const data = radiationState.history.slice(-dataCount)  // 获取最新数据
      if (data.length < 2) {
        // 绘制无数据提示
        ctx.setFillStyle('#64748b')
        ctx.setFontSize(16)
        ctx.setTextAlign('center')
        ctx.fillText('暂无数据', width / 2, height / 2)
        ctx.draw()
        return
      }
      
      // 绘制网格线 - 更美观的样式
      ctx.setStrokeStyle('rgba(226, 232, 240, 0.4)')
      ctx.setLineWidth(0.8)
      
      // 垂直网格线
      for (let i = 1; i < 10; i++) {  // 去掉边界线
        const x = padding + (i / 10) * (width - padding * 2)
        ctx.beginPath()
        ctx.moveTo(x, padding)
        ctx.lineTo(x, height - padding)
        ctx.stroke()
      }
      
      // 水平网格线
      for (let i = 1; i < 5; i++) {  // 去掉边界线
        const y = padding + (i / 5) * (height - padding * 2)
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }
      
      // 绘制坐标轴
      ctx.setStrokeStyle('#64748b')
      ctx.setLineWidth(2)
      
      // X轴
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)
      ctx.lineTo(width - padding, height - padding)
      ctx.stroke()
      
      // Y轴
      ctx.beginPath()
      ctx.moveTo(padding, padding)
      ctx.lineTo(padding, height - padding)
      ctx.stroke()
      
      // 绘制剂量率曲线
      const doseRates = data.map(item => item.doseRate)
      const maxDose = Math.max(...doseRates)
      const minDose = Math.min(...doseRates)
      const doseRange = (maxDose - minDose) * 1.2 || 1  // 增加显示范围
      const adjustedMinDose = minDose - (maxDose - minDose) * 0.1
      
      // 计算坐标点
      const dosePoints = data.map((item, index) => ({
        x: padding + (index / (data.length - 1)) * (width - padding * 2),
        y: height - padding - ((item.doseRate - adjustedMinDose) / doseRange) * (height - padding * 2)
      }))
      
      // 绘制剂量率渐变填充
      if (dosePoints.length > 1) {
        const doseGradient = ctx.createLinearGradient(0, padding, 0, height - padding)
        doseGradient.addColorStop(0, 'rgba(0, 180, 216, 0.4)')
        doseGradient.addColorStop(0.5, 'rgba(0, 180, 216, 0.2)')
        doseGradient.addColorStop(1, 'rgba(0, 180, 216, 0.05)')
        ctx.setFillStyle(doseGradient)
        
        ctx.beginPath()
        ctx.moveTo(dosePoints[0].x, height - padding)
        dosePoints.forEach((point, index) => {
        if (index === 0) {
            ctx.lineTo(point.x, point.y)
        } else {
            // 平滑曲线
            const prevPoint = dosePoints[index - 1]
            const cpx = (prevPoint.x + point.x) / 2
            ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
          }
        })
        ctx.lineTo(dosePoints[dosePoints.length - 1].x, height - padding)
        ctx.closePath()
        ctx.fill()
      }
      
      // 绘制剂量率曲线
      ctx.beginPath()
      ctx.setStrokeStyle('#00b4d8')
      ctx.setLineWidth(4)
      ctx.setShadow(0, 3, 8, 'rgba(0, 180, 216, 0.3)')
      
      dosePoints.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          // 平滑曲线
          const prevPoint = dosePoints[index - 1]
          const cpx = (prevPoint.x + point.x) / 2
          ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
        }
      })
      ctx.stroke()
      
      // 绘制计数率曲线（缩放显示）
      const cpsData = data.map(item => item.cps)
      const maxCps = Math.max(...cpsData)
      const minCps = Math.min(...cpsData)
      const cpsRange = (maxCps - minCps) * 1.2 || 1
      const adjustedMinCps = minCps - (maxCps - minCps) * 0.1
      
      // 计算CPS坐标点
      const cpsPoints = data.map((item, index) => ({
        x: padding + (index / (data.length - 1)) * (width - padding * 2),
        y: height - padding - ((item.cps - adjustedMinCps) / cpsRange) * (height - padding * 2) * 0.6  // 占60%高度
      }))
      
      // 绘制CPS渐变填充
      if (cpsPoints.length > 1) {
        const cpsGradient = ctx.createLinearGradient(0, height - padding, 0, height - padding - (height - padding * 2) * 0.6)
        cpsGradient.addColorStop(0, 'rgba(16, 185, 129, 0.3)')
        cpsGradient.addColorStop(1, 'rgba(16, 185, 129, 0.05)')
        ctx.setFillStyle(cpsGradient)
        
      ctx.beginPath()
        ctx.moveTo(cpsPoints[0].x, height - padding)
        cpsPoints.forEach((point, index) => {
        if (index === 0) {
            ctx.lineTo(point.x, point.y)
        } else {
            const prevPoint = cpsPoints[index - 1]
            const cpx = (prevPoint.x + point.x) / 2
            ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
          }
        })
        ctx.lineTo(cpsPoints[cpsPoints.length - 1].x, height - padding)
        ctx.closePath()
        ctx.fill()
      }
      
      // 绘制CPS曲线
      ctx.beginPath()
      ctx.setStrokeStyle('#10b981')
      ctx.setLineWidth(3)
      ctx.setShadow(0, 2, 6, 'rgba(16, 185, 129, 0.3)')
      
      cpsPoints.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          const prevPoint = cpsPoints[index - 1]
          const cpx = (prevPoint.x + point.x) / 2
          ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
        }
      })
      ctx.stroke()
      
      // 重置阴影
      ctx.setShadow(0, 0, 0, 'transparent')
      
      // 绘制数据点（仅显示关键点）
      const showPointInterval = Math.max(1, Math.floor(data.length / 20))  // 最多显示20个点
      dosePoints.forEach((point, index) => {
        if (index % showPointInterval === 0 || index === dosePoints.length - 1) {
          // 外圈
          ctx.beginPath()
          ctx.setFillStyle('#ffffff')
          ctx.arc(point.x, point.y, 5, 0, 2 * Math.PI)
          ctx.fill()
          // 内圈
          ctx.beginPath()
          ctx.setFillStyle('#00b4d8')
          ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI)
          ctx.fill()
        }
      })
      
      // CPS数据点
      cpsPoints.forEach((point, index) => {
        if (index % showPointInterval === 0 || index === cpsPoints.length - 1) {
          ctx.beginPath()
          ctx.setFillStyle('#ffffff')
          ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI)
          ctx.fill()
          ctx.beginPath()
          ctx.setFillStyle('#10b981')
          ctx.arc(point.x, point.y, 2.5, 0, 2 * Math.PI)
          ctx.fill()
        }
      })
      
      ctx.draw()
    }

    onMounted(() => {
      setTimeout(() => {
        initChart()
      }, 500)
    })

      // 新增的交互方法
    const focusOnDoseRate = () => {
      uni.showToast({
        title: '聚焦剂量率数据',
        icon: 'none'
      })
    }

    const focusOnCumulative = () => {
      uni.showToast({
        title: '聚焦累积剂量',
        icon: 'none'
      })
    }

    const focusOnCPS = () => {
      uni.showToast({
        title: '聚焦计数率数据',
        icon: 'none'
      })
    }

    const openChartSettings = () => {
      uni.showActionSheet({
        itemList: ['切换主题', '调整精度', '数据源设置'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              uni.showToast({ title: '主题切换', icon: 'none' })
              break
            case 1:
              uni.showToast({ title: '精度调整', icon: 'none' })
              break
            case 2:
              uni.showToast({ title: '数据源设置', icon: 'none' })
              break
          }
        }
      })
    }

    const viewDetailedStats = () => {
      uni.navigateTo({
        url: '/pages/stats/stats'
      })
    }

    const toggleFabMenu = () => {
      fabMenuOpen.value = !fabMenuOpen.value
    }

    const shareChart = () => {
      uni.share({
        provider: 'weixin',
        type: 1,
        title: '辐射监测数据图表',
        summary: '智能辐射监测系统生成的数据分析图表',
        success: () => {
          uni.showToast({ title: '分享成功', icon: 'success' })
        }
      })
    }

    const resetChart = () => {
      selectedTimeRange.value = '24h'
      drawChart()
      uni.showToast({ title: '视图已重置', icon: 'success' })
    }

      onUnmounted(() => {
      if (updateInterval) {
        clearInterval(updateInterval)
      }
    })

    return {
      radiationState,
      selectedTimeRange,
      isLoading,
      chartWidth,
      chartHeight,
      doseRateTrend,
      cpsTrend,
      todayStats,
      todayAlerts,
      recentHistory,
      selectTimeRange,
      refreshChart,
      exportChart,
      exportData,
      getAlertCount,
      formatTime,
      getStatusClass,
      getStatusText,
      fabMenuOpen,
      focusOnDoseRate,
      focusOnCumulative,
      focusOnCPS,
      openChartSettings,
      viewDetailedStats,
      toggleFabMenu,
      shareChart,
      resetChart,
      maxDoseRate: computed(() => Math.max(...radiationState.history.slice(0, 50).map(item => item.doseRate))),
      minDoseRate: computed(() => Math.min(...radiationState.history.slice(0, 50).map(item => item.doseRate))),
      avgDoseRate: computed(() => {
        const data = radiationState.history.slice(0, 50)
        return data.reduce((sum, item) => sum + item.doseRate, 0) / data.length || 0
      }),
      changeRate: computed(() => {
        const recent = radiationState.history.slice(0, 10)
        if (recent.length < 2) return 0
        const current = recent[0].doseRate
        const previous = recent[recent.length - 1].doseRate
        return ((current - previous) / previous) * 100
      }),
      maxDoseProgress: computed(() => 100),
      minDoseProgress: computed(() => 50),
      avgDoseProgress: computed(() => 75)
    }
  }
}
</script>

<style scoped>
/* 现代化页面容器 */
.charts-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding-bottom: 120rpx; /* 为底部导航留空间 */
  position: relative;
  overflow-x: hidden;
}

/* 炫酷的页面头部 */
.charts-header {
  position: relative;
  background: linear-gradient(135deg, #00b4d8 0%, #0096c7 50%, #0077b6 100%);
  padding: 60rpx 40rpx 80rpx;
  border-radius: 0 0 60rpx 60rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 180, 216, 0.2);
  overflow: hidden;
}

.header-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.header-content {
  position: relative;
  z-index: 2;
  text-align: center;
  margin-bottom: 40rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 12rpx;
  display: block;
}

.header-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  display: block;
}

/* 现代化时间范围选择器 */
.time-range-container {
  position: relative;
  z-index: 2;
}

.range-selector {
  display: flex;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 25rpx;
  padding: 8rpx;
  gap: 4rpx;
}

.range-option {
  position: relative;
  flex: 1;
  padding: 24rpx 20rpx;
  text-align: center;
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.range-option.active {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  transform: translateY(-2rpx);
}

.range-text {
  font-size: 28rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.range-option.active .range-text {
  color: #00b4d8;
}

.active-indicator {
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 8rpx;
  height: 8rpx;
  background: #00b4d8;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.range-option.active .active-indicator {
  opacity: 1;
}

/* 炫酷的数据概览卡片 */
.overview-section {
  padding: 40rpx;
  margin-top: -40rpx;
  position: relative;
  z-index: 3;
}

.overview-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.overview-card:hover {
  transform: translateY(-8rpx) scale(1.02);
  box-shadow: 0 32rpx 64rpx rgba(0, 0, 0, 0.1);
}

.overview-card.primary {
  background: linear-gradient(135deg, rgba(0, 180, 216, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.overview-card.secondary {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.overview-card.tertiary {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.card-bg-effect {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
  transition: transform 0.6s ease;
}

.overview-card:hover .card-bg-effect {
  transform: scale(1.5) rotate(30deg);
}

.card-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.metric-icon {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 50%;
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.1);
}

.icon-text {
  font-size: 48rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.icon-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 180, 216, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.overview-card:hover .icon-glow {
  opacity: 1;
}

.metric-data {
  flex: 1;
}

.metric-label {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.metric-value {
  font-size: 56rpx;
  font-weight: 700;
  color: #0f172a;
  line-height: 1.2;
  display: block;
}

.metric-unit {
  font-size: 24rpx;
  color: #94a3b8;
  font-weight: 500;
  margin-top: 4rpx;
  display: block;
}

.trend-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20rpx;
  min-width: 120rpx;
}

.trend-arrow {
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.trend-label {
  font-size: 20rpx;
  font-weight: 600;
  text-align: center;
}

.trend-container.up {
  background: rgba(16, 185, 129, 0.1);
}

.trend-container.up .trend-label {
  color: #10b981;
}

.trend-container.down {
  background: rgba(239, 68, 68, 0.1);
}

.trend-container.down .trend-label {
  color: #ef4444;
}

.trend-container.stable {
  background: rgba(156, 163, 175, 0.1);
}

.trend-container.stable .trend-label {
  color: #6b7280;
}

.card-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
  pointer-events: none;
}

.overview-card:hover .card-shine {
  left: 100%;
}

/* 超炫酷的主图表区域 */
.main-chart-section {
  padding: 0 40rpx;
  margin-bottom: 40rpx;
}

.chart-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 40rpx;
  padding: 48rpx;
  box-shadow: 0 24rpx 48rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
  position: relative;
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
  padding-bottom: 24rpx;
  border-bottom: 2px solid rgba(226, 232, 240, 0.5);
}

.chart-title-container {
  flex: 1;
}

.chart-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 8rpx;
  display: block;
}

.chart-subtitle {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
  display: block;
}

.chart-controls {
  display: flex;
  gap: 16rpx;
}

.control-btn {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
}

.control-btn:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.control-btn.refresh:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

.control-btn.export:hover {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
}

.control-btn.settings:hover {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.btn-icon {
  font-size: 32rpx;
  position: relative;
  z-index: 2;
}

.btn-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.control-btn:active .btn-ripple {
  width: 100rpx;
  height: 100rpx;
}

.chart-container {
  position: relative;
}

.chart-wrapper {
  position: relative;
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.chart-canvas {
  width: 100%;
  height: 300px;
  border-radius: 16rpx;
}

.chart-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4rpx);
  border-radius: 24rpx;
}

.loading-animation {
  text-align: center;
}

.loading-dots {
  display: flex;
  gap: 12rpx;
  justify-content: center;
  margin-bottom: 24rpx;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  background: #00b4d8;
  border-radius: 50%;
  animation: dotPulse 1.5s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dotPulse {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  30% {
    transform: scale(1.3);
    opacity: 1;
  }
}

.loading-text {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
}

/* 美化的图表图例 */
.enhanced-legend {
  display: flex;
  justify-content: center;
  gap: 48rpx;
  margin-top: 32rpx;
  padding: 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 20rpx;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.legend-indicator {
  position: relative;
  display: flex;
  align-items: center;
}

.legend-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.legend-item.dose-rate .legend-dot {
  background: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);
}

.legend-item.count-rate .legend-dot {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.legend-line {
  width: 32rpx;
  height: 3rpx;
  margin-left: 8rpx;
  border-radius: 2rpx;
}

.legend-item.dose-rate .legend-line {
  background: linear-gradient(90deg, #00b4d8, #0096c7);
}

.legend-item.count-rate .legend-line {
  background: linear-gradient(90deg, #10b981, #059669);
}

.legend-content {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.legend-label {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 600;
}

.legend-unit {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

/* 精美的统计数据网格 */
.stats-section {
  padding: 0 40rpx;
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding: 0 8rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.section-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.section-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #0f172a;
}

.section-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 180, 216, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.section-action:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 180, 216, 0.4);
}

.action-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
}

.action-arrow {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.section-action:hover .action-arrow {
  transform: translateX(4rpx);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 28rpx;
  padding: 32rpx;
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-6rpx);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.stat-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.stat-title {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 600;
}

.stat-content {
  margin-bottom: 20rpx;
}

.stat-value {
  font-size: 40rpx;
  font-weight: 700;
  color: #0f172a;
  line-height: 1.2;
  display: block;
}

.stat-unit {
  font-size: 20rpx;
  color: #94a3b8;
  font-weight: 500;
  margin-top: 4rpx;
  display: block;
}

.stat-progress {
  height: 8rpx;
  background: rgba(226, 232, 240, 0.5);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #00b4d8 0%, #0096c7 100%);
  border-radius: 4rpx;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-bar.change {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: 140rpx;
  right: 40rpx;
  z-index: 1000;
}

.fab {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.fab.main {
  background: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
}

.fab-icon {
  font-size: 40rpx;
  color: #ffffff;
  font-weight: 600;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.fab.main:hover .fab-icon {
  transform: rotate(45deg);
}

.fab-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
}

.fab:active .fab-ripple {
  width: 140rpx;
  height: 140rpx;
}

.fab-menu {
  position: absolute;
  bottom: 140rpx;
  right: 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fab-menu.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.fab-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 50rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 200rpx;
  justify-content: flex-end;
}

.fab-item:hover {
  transform: translateX(-8rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
}

.fab-text {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 600;
}

.fab-item .fab-icon {
  font-size: 28rpx;
  color: #00b4d8;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 180, 216, 0.1);
  border-radius: 50%;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .charts-header {
    padding: 40rpx 30rpx 60rpx;
}

  .header-title {
    font-size: 40rpx;
  }
  
  .overview-section {
    padding: 30rpx;
  }
  
  .overview-card {
    padding: 30rpx;
  }
  
  .card-content {
    flex-direction: column;
  text-align: center;
    gap: 24rpx;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
}

  .chart-card {
    padding: 32rpx;
}

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 24rpx;
}

.fab-container {
  bottom: 120rpx;
    right: 30rpx;
  }
}

/* 高级动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
}
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.overview-card {
  animation: fadeInUp 0.6s ease-out;
}

.overview-card:nth-child(2) {
  animation-delay: 0.1s;
}

.overview-card:nth-child(3) {
  animation-delay: 0.2s;
}

.chart-card {
  animation: fadeInUp 0.8s ease-out;
}

.stat-card {
  animation: slideInRight 0.6s ease-out;
}

.stat-card:nth-child(even) {
  animation-delay: 0.1s;
}

.fab {
  animation: pulse 2s infinite;
}
</style> 