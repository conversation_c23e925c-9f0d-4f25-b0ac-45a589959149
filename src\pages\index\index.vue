<template>
  <view class="index-container">
    <!-- 欢迎头部区域 -->
    <view class="header-section">
      <view class="welcome-banner">
        <text class="welcome-icon">🛡️</text>
        <text class="welcome-title">智能辐射监测系统</text>
        <text class="welcome-subtitle">专业级实时监测，守护您的健康安全</text>
      </view>
      
      <view class="user-profile">
        <view class="avatar">
          <text class="avatar-text">👤</text>
        </view>
        <view class="user-info">
          <text class="user-name">用户</text>
          <text class="user-status">在线监测中</text>
        </view>
        <view class="device-status">
          <view class="status-dot connected"></view>
          <text class="status-text">设备已连接</text>
        </view>
      </view>
    </view>

    <!-- 时间和天气信息 -->
    <view class="time-weather-section">
      <view class="time-card">
        <text class="time-icon">🕒</text>
        <view class="time-info">
          <text class="current-time">{{ currentTime }}</text>
          <text class="current-date">{{ currentDate }}</text>
        </view>
      </view>
      <view class="weather-card">
        <text class="weather-icon">🌤️</text>
        <view class="weather-details">
          <text class="temperature">{{ weather.temperature }}°C</text>
          <text class="weather-desc">{{ weather.description }}</text>
        </view>
        <view class="weather-extra">
          <text class="humidity">湿度 {{ weather.humidity }}%</text>
          <text class="wind">风速 {{ weather.windSpeed }}km/h</text>
        </view>
      </view>
    </view>

    <!-- 快速操作 -->
    <view class="quick-actions">
      <view class="section-title">
        <text class="section-icon">⚡</text>
        <text class="section-text">快速访问</text>
      </view>
      <view class="actions-grid">
        <view class="action-item dashboard" @tap="navigateTo('/pages/dashboard/dashboard')">
          <text class="action-icon">📊</text>
          <text class="action-text">仪表盘</text>
        </view>
        <view class="action-item health" @tap="navigateTo('/pages/health/health')">
          <text class="action-icon">❤️</text>
          <text class="action-text">健康监测</text>
        </view>
        <view class="action-item charts" @tap="navigateTo('/pages/charts/charts')">
          <text class="action-icon">📈</text>
          <text class="action-text">数据分析</text>
        </view>
        <view class="action-item settings" @tap="navigateTo('/pages/settings/settings')">
          <text class="action-icon">⚙️</text>
          <text class="action-text">设置</text>
        </view>
      </view>
    </view>

    <!-- 数据概览 -->
    <view class="stats-overview">
      <view class="section-title">
        <text class="section-icon">📋</text>
        <text class="section-text">实时概览</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-header">
            <text class="stat-icon">☢️</text>
            <text class="stat-title">当前剂量率</text>
          </view>
          <text class="stat-value">{{ radiationState.currentData.doseRate.toFixed(3) }}</text>
          <text class="stat-unit">μSv/h</text>
          <view class="mini-chart">
            <canvas canvas-id="doseChart" class="chart-canvas"></canvas>
          </view>
        </view>
        
        <view class="stat-item">
          <view class="stat-header">
            <text class="stat-icon">📱</text>
            <text class="stat-title">设备状态</text>
          </view>
          <text class="stat-value">{{ deviceState.battery.level.toFixed(1) }}</text>
          <text class="stat-unit">% 电量</text>
          <view class="trend-indicator" :class="batteryTrend">
            <text class="trend-text">{{ batteryStatus }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近活动 -->
    <view class="recent-activity">
      <view class="section-title">
        <text class="section-icon">📝</text>
        <text class="section-text">最近活动</text>
      </view>
      <view class="activity-timeline">
        <view class="activity-item" v-for="(activity, index) in recentActivities" :key="index">
          <view class="activity-indicator" :class="activity.type"></view>
          <view class="activity-content">
            <text class="activity-title-text">{{ activity.title }}</text>
            <text class="activity-desc">{{ activity.description }}</text>
            <view class="activity-meta">
              <text class="activity-time">{{ formatTime(activity.timestamp) }}</text>
              <text class="activity-type">{{ activity.category }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 悬浮操作按钮 -->
    <view class="fab-container" :class="{ active: fabMenuOpen }" @tap="toggleFabMenu">
      <view class="fab-main">
        <text class="fab-icon">{{ fabMenuOpen ? '✕' : '📋' }}</text>
      </view>
      <view class="fab-menu">
        <view class="fab-item scan" @tap="quickScan">
          <text class="fab-item-icon">🔍</text>
        </view>
        <view class="fab-item alert" @tap="toggleAlert">
          <text class="fab-item-icon">🚨</text>
        </view>
        <view class="fab-item sync" @tap="syncData">
          <text class="fab-item-icon">🔄</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState, deviceState } from '../../utils/dataStore.js'

export default {
  name: 'IndexPage',
  setup() {
    const currentTime = ref('')
    const currentDate = ref('')
    const fabMenuOpen = ref(false)
    let timeInterval = null

    const weather = ref({
      temperature: 25,
      description: '晴朗',
      humidity: 65,
      windSpeed: 8
    })

    const recentActivities = ref([
      {
        title: '辐射检测正常',
        description: '设备运行良好，数据稳定',
        timestamp: Date.now() - 300000,
        type: 'success',
        category: '监测'
      },
      {
        title: '电池电量充足',
        description: '当前电量85%，续航良好',
        timestamp: Date.now() - 600000,
        type: 'info',
        category: '设备'
      },
      {
        title: '数据同步完成',
        description: '已上传到云端服务器',
        timestamp: Date.now() - 900000,
        type: 'success',
        category: '同步'
      }
    ])

    const batteryTrend = computed(() => {
      const level = deviceState.battery.level
      if (level > 50) return 'high'
      if (level > 20) return 'medium'
      return 'low'
    })

    const batteryStatus = computed(() => {
      const level = deviceState.battery.level
      if (deviceState.battery.charging) return '充电中'
      if (level > 50) return '电量充足'
      if (level > 20) return '电量正常'
      return '电量偏低'
    })

    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      currentDate.value = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }

    const navigateTo = (url) => {
      uni.navigateTo({ url })
    }

    const toggleFabMenu = () => {
      fabMenuOpen.value = !fabMenuOpen.value
    }

    const quickScan = () => {
      fabMenuOpen.value = false
      uni.showToast({
        title: '开始快速扫描',
        icon: 'success'
      })
    }

    const toggleAlert = () => {
      fabMenuOpen.value = false
      uni.showToast({
        title: '报警设置已切换',
        icon: 'success'
      })
    }

    const syncData = () => {
      fabMenuOpen.value = false
      uni.showLoading({
        title: '同步中...'
      })
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '数据同步成功',
          icon: 'success'
        })
      }, 2000)
    }

    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      const now = Date.now()
      const diff = now - timestamp
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return date.toLocaleDateString()
    }

    onMounted(() => {
      updateTime()
      timeInterval = setInterval(updateTime, 1000)
    })

    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })

    return {
      radiationState,
      deviceState,
      currentTime,
      currentDate,
      weather,
      recentActivities,
      batteryTrend,
      batteryStatus,
      fabMenuOpen,
      navigateTo,
      toggleFabMenu,
      quickScan,
      toggleAlert,
      syncData,
      formatTime
    }
  }
}
</script>

<style scoped>
.index-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  padding: 24rpx;
  position: relative;
}

.index-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 20%, rgba(0, 180, 216, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* 头部区域 - 现代浅色设计 */
.header-section {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8rpx 32rpx rgba(15, 23, 42, 0.08);
  overflow: hidden;
}

.header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #00b4d8, #0096c7, #10b981, #8b5cf6);
  border-radius: 32rpx 32rpx 0 0;
}

.welcome-banner {
  text-align: center;
  margin-bottom: 32rpx;
}

.welcome-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  color: #00b4d8;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 180, 216, 0.2));
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8rpx); }
}

.welcome-title {
  font-size: 42rpx;
  color: #0f172a;
  font-weight: 800;
  margin-bottom: 12rpx;
  background: linear-gradient(135deg, #334155, #1e293b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.5;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.avatar-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  box-shadow: 0 4rpx 12rpx rgba(0, 180, 216, 0.3);
}

.user-details {
  flex: 1;
  margin-left: 16rpx;
}

.user-name {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 6rpx;
}

.user-status {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
}

.device-status-mini {
  padding: 16rpx 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.device-status-mini:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(203, 213, 225, 0.8);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.08);
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  position: relative;
}

.status-dot.online {
  background: #10b981;
  box-shadow: 0 0 0 3rpx rgba(16, 185, 129, 0.2);
  animation: pulse 2s infinite;
}

.status-dot.offline {
  background: #94a3b8;
  box-shadow: 0 0 0 3rpx rgba(148, 163, 184, 0.2);
}

.status-text {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 600;
}

/* 时间天气区域 - 优雅设计 */
.time-weather-section {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.time-card, .weather-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  position: relative;
  overflow: hidden;
}

.time-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
  border-radius: 24rpx 24rpx 0 0;
}

.weather-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #f59e0b, #d97706);
  border-radius: 24rpx 24rpx 0 0;
}

.time-card:hover, .weather-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(15, 23, 42, 0.12);
  border-color: rgba(203, 213, 225, 0.9);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.card-icon {
  font-size: 28rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.card-title-text {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 700;
}

.current-time {
  font-size: 48rpx;
  color: #0f172a;
  font-weight: 800;
  margin-bottom: 8rpx;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(15, 23, 42, 0.08);
}

.current-date {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
}

.weather-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.weather-main {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.weather-icon {
  font-size: 56rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.weather-details {
  flex: 1;
}

.temperature {
  font-size: 36rpx;
  color: #0f172a;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 6rpx;
  text-shadow: 0 2rpx 4rpx rgba(15, 23, 42, 0.08);
}

.weather-desc {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.weather-extra {
  text-align: right;
}

.humidity, .wind {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
  line-height: 1.4;
}

/* 快速操作区域 - 现代设计 */
.quick-actions {
  position: relative;
  z-index: 2;
  margin-bottom: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

.section-icon {
  font-size: 28rpx;
  color: #00b4d8;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 180, 216, 0.2));
}

.section-text {
  font-size: 30rpx;
  color: #0f172a;
  font-weight: 700;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.action-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 28rpx 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.action-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-item.dashboard::before {
  background: linear-gradient(90deg, #00b4d8, #0096c7);
}

.action-item.health::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.action-item.charts::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.action-item.settings::before {
  background: linear-gradient(90deg, #64748b, #475569);
}

.action-item:hover {
  transform: translateY(-6rpx);
  box-shadow: 0 16rpx 40rpx rgba(15, 23, 42, 0.15);
  border-color: rgba(203, 213, 225, 0.9);
}

.action-item:hover::before {
  height: 4rpx;
}

.action-item:active {
  transform: translateY(-3rpx) scale(0.98);
  box-shadow: 0 8rpx 25rpx rgba(15, 23, 42, 0.12);
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.action-text {
  font-size: 22rpx;
  color: #0f172a;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.action-desc {
  font-size: 18rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.3;
}

/* 统计卡片区域 - 优雅布局 */
.stats-overview {
  position: relative;
  z-index: 2;
  margin-bottom: 32rpx;
}

.stats-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  border-radius: 24rpx 24rpx 0 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card.radiation::before {
  background: linear-gradient(90deg, #06ffa5, #059669);
}

.stat-card.device::before {
  background: linear-gradient(90deg, #00b4d8, #0096c7);
}

.stat-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(15, 23, 42, 0.12);
  border-color: rgba(203, 213, 225, 0.9);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.stat-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.stat-icon {
  font-size: 28rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.stat-label {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 700;
}

.stat-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 600;
  border: 1px solid;
}

.stat-status.safe {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.2);
}

.stat-status.online {
  background: rgba(0, 180, 216, 0.1);
  color: #0077b6;
  border-color: rgba(0, 180, 216, 0.2);
}

.stat-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20rpx;
}

.stat-value {
  font-size: 44rpx;
  color: #0f172a;
  font-weight: 800;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(15, 23, 42, 0.08);
}

.stat-unit {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 600;
  margin-left: 6rpx;
}

.stat-chart {
  width: 100rpx;
  height: 60rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 12rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  overflow: hidden;
}

.mini-chart {
  width: 100%;
  height: 100%;
}

.stat-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 600;
  border: 1px solid;
}

.stat-change.up {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.2);
}

.stat-change.down {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.2);
}

.stat-change.stable {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border-color: rgba(245, 158, 11, 0.2);
}

.change-icon {
  font-size: 16rpx;
}

.change-text {
  font-size: 18rpx;
  font-weight: 600;
}

.stat-time {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
}

/* 最近活动区域 - 现代时间线 */
.recent-activity {
  position: relative;
  z-index: 2;
}

.activity-list {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  position: relative;
  overflow: hidden;
}

.activity-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #8b5cf6, #7c3aed, #06ffa5);
  border-radius: 24rpx 24rpx 0 0;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.activity-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.activity-icon {
  font-size: 28rpx;
  color: #8b5cf6;
  filter: drop-shadow(0 2rpx 4rpx rgba(139, 92, 246, 0.2));
}

.activity-title-text {
  font-size: 26rpx;
  color: #0f172a;
  font-weight: 700;
}

.view-all-btn {
  padding: 12rpx 20rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 12rpx;
  color: #64748b;
  font-size: 20rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.view-all-btn:hover {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: #ffffff;
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.2);
}

.activity-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 24rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(203, 213, 225, 0.8);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.08);
}

.activity-indicator {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.activity-indicator.info {
  background: linear-gradient(135deg, #00b4d8, #0096c7);
}

.activity-indicator.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.activity-indicator.success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.activity-indicator.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.activity-content {
  flex: 1;
}

.activity-title-text {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.activity-desc {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-time {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
}

.activity-type {
  padding: 4rpx 12rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 10rpx;
  font-size: 16rpx;
  color: #64748b;
  font-weight: 600;
}

/* 底部导航增强 */
.bottom-nav-placeholder {
  height: 120rpx;
  margin-top: 32rpx;
}

/* 悬浮按钮 - 多功能设计 */
.fab-container {
  position: fixed;
  bottom: 140rpx;
  right: 40rpx;
  z-index: 100;
}

.fab-main {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 25rpx rgba(0, 180, 216, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.fab-main:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 15rpx rgba(0, 180, 216, 0.5);
}

.fab-icon {
  font-size: 40rpx;
  color: #ffffff;
}

.fab-menu {
  position: absolute;
  bottom: 120rpx;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.fab-container.active .fab-menu {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

.fab-item {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.fab-item.scan {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.fab-item.alert {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.fab-item.sync {
  background: linear-gradient(135deg, #10b981, #059669);
}

.fab-item:active {
  transform: scale(0.9);
}

/* 响应式设计优化 */
@media (max-width: 750rpx) {
  .time-weather-section {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }
  
  .stats-cards {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .index-container {
    padding: 16rpx;
  }
  
  .header-section {
    padding: 32rpx 24rpx;
  }
  
  .user-info {
    flex-direction: column;
    gap: 16rpx;
    text-align: center;
  }
  
  .current-time {
    font-size: 36rpx;
  }
  
  .temperature {
    font-size: 28rpx;
  }
  
  .stat-value {
  font-size: 36rpx;
  }
  
  .activity-item {
    flex-direction: column;
    gap: 12rpx;
    text-align: center;
  }
  
  .activity-meta {
    flex-direction: column;
    gap: 8rpx;
    align-items: center;
  }
}

/* 加载动画 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* 通知样式 */
.notification-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 24rpx;
  height: 24rpx;
  background: #ef4444;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  color: #ffffff;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.3);
  animation: pulse 2s infinite;
}

/* 特殊效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
  background: linear-gradient(135deg, #00b4d8, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-soft {
  box-shadow: 0 4rpx 6rpx -1rpx rgba(0, 0, 0, 0.1), 0 2rpx 4rpx -1rpx rgba(0, 0, 0, 0.06);
}

.shadow-medium {
  box-shadow: 0 10rpx 15rpx -3rpx rgba(0, 0, 0, 0.1), 0 4rpx 6rpx -2rpx rgba(0, 0, 0, 0.05);
}

.shadow-large {
  box-shadow: 0 20rpx 25rpx -5rpx rgba(0, 0, 0, 0.1), 0 10rpx 10rpx -5rpx rgba(0, 0, 0, 0.04);
}
</style>
