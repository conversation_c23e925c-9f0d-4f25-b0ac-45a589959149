<template>
  <view class="toast-container" v-if="visible" :class="[`toast-${type}`, animationClass]">
    <view class="toast-content">
      <view class="toast-icon">
        <text v-if="type === 'success'">✓</text>
        <text v-else-if="type === 'error'">✗</text>
        <text v-else-if="type === 'warning'">⚠</text>
        <text v-else>ℹ</text>
      </view>
      <view class="toast-message">{{ message }}</view>
      <view class="toast-countdown" v-if="showCountdown">{{ countdown }}</view>
    </view>
    <view class="toast-progress" :style="{ width: progressWidth + '%' }"></view>
  </view>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'Toast',
  props: {
    message: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'info', // success, error, warning, info
      validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
    },
    duration: {
      type: Number,
      default: 3000
    },
    showCountdown: {
      type: Boolean,
      default: true
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const visible = ref(false)
    const countdown = ref(0)
    const progressWidth = ref(100)
    const animationClass = ref('')
    
    let timer = null
    let progressTimer = null
    
    const show = () => {
      visible.value = true
      animationClass.value = 'toast-enter'
      countdown.value = Math.ceil(props.duration / 1000)
      progressWidth.value = 100
      
      // 倒计时
      timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          hide()
        }
      }, 1000)
      
      // 进度条
      const progressStep = 100 / (props.duration / 100)
      progressTimer = setInterval(() => {
        progressWidth.value -= progressStep
        if (progressWidth.value <= 0) {
          progressWidth.value = 0
        }
      }, 100)
      
      // 自动隐藏
      setTimeout(() => {
        hide()
      }, props.duration)
    }
    
    const hide = () => {
      animationClass.value = 'toast-leave'
      setTimeout(() => {
        visible.value = false
        emit('close')
        clearInterval(timer)
        clearInterval(progressTimer)
      }, 300)
    }
    
    return {
      visible,
      countdown,
      progressWidth,
      animationClass,
      show,
      hide
    }
  }
}
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  min-width: 300rpx;
  max-width: 600rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20rpx);
}

.toast-content {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  position: relative;
}

.toast-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.toast-message {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.toast-countdown {
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #666;
  min-width: 40rpx;
  text-align: center;
}

.toast-progress {
  height: 4rpx;
  background: currentColor;
  transition: width 0.1s linear;
}

/* 类型样式 */
.toast-success .toast-icon {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.toast-success .toast-progress {
  color: #4CAF50;
}

.toast-error .toast-icon {
  background: linear-gradient(135deg, #f44336, #d32f2f);
}

.toast-error .toast-progress {
  color: #f44336;
}

.toast-warning .toast-icon {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.toast-warning .toast-progress {
  color: #ff9800;
}

.toast-info .toast-icon {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

.toast-info .toast-progress {
  color: #2196F3;
}

/* 动画 */
.toast-enter {
  animation: toastSlideIn 0.3s ease-out;
}

.toast-leave {
  animation: toastSlideOut 0.3s ease-in;
}

@keyframes toastSlideIn {
  from {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

@keyframes toastSlideOut {
  from {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  to {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
}
</style>
