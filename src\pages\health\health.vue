<template>
  <view class="health-container">
    <!-- Toast容器 -->
    <ToastContainer />

    <!-- 健康状态总览 -->
    <view class="health-overview">
      <view class="overview-header">
        <text class="overview-title">健康监测</text>
        <view class="health-score">
          <text class="score-value">{{ healthScore }}</text>
          <text class="score-label">健康指数</text>
        </view>
      </view>
      
      <view class="status-indicator" :class="healthStatusClass">
        <text class="status-icon">{{ healthStatusIcon }}</text>
        <text class="status-text">{{ healthStatusText }}</text>
      </view>
    </view>

    <!-- 核心健康指标卡片 -->
    <view class="health-cards">
      <!-- 心率卡片 -->
      <view class="health-card heart-rate">
        <view class="card-header">
          <text class="card-icon">❤️</text>
          <text class="card-title">心率</text>
          <view class="card-status" :class="heartRateStatus.class">
            <text class="status-dot"></text>
          </view>
        </view>
        
        <view class="card-main">
          <view class="metric-display">
            <text class="metric-value">{{ healthState.current.heartRate }}</text>
            <text class="metric-unit">BPM</text>
          </view>
          
          <view class="metric-chart">
            <canvas canvas-id="heartRateChart" class="mini-chart"></canvas>
          </view>
        </view>
        
        <view class="card-footer">
          <text class="metric-label">{{ heartRateStatus.text }}</text>
          <text class="metric-range">正常: 60-100</text>
        </view>
      </view>

      <!-- 血氧卡片 -->
      <view class="health-card spo2">
        <view class="card-header">
          <text class="card-icon">🫁</text>
          <text class="card-title">血氧</text>
          <view class="card-status" :class="spO2Status.class">
            <text class="status-dot"></text>
          </view>
        </view>
        
        <view class="card-main">
          <view class="metric-display">
            <text class="metric-value">{{ healthState.current.spO2 }}</text>
            <text class="metric-unit">%</text>
          </view>
          
          <view class="metric-chart">
            <canvas canvas-id="spO2Chart" class="mini-chart"></canvas>
          </view>
        </view>
        
        <view class="card-footer">
          <text class="metric-label">{{ spO2Status.text }}</text>
          <text class="metric-range">正常: >95%</text>
        </view>
      </view>

      <!-- 体温卡片 -->
      <view class="health-card body-temp">
        <view class="card-header">
          <text class="card-icon">🌡️</text>
          <text class="card-title">体温</text>
          <view class="card-status" :class="bodyTempStatus.class">
            <text class="status-dot"></text>
          </view>
        </view>
        
        <view class="card-main">
          <view class="metric-display">
            <text class="metric-value">{{ healthState.current.bodyTemp.toFixed(1) }}</text>
            <text class="metric-unit">°C</text>
          </view>
          
          <view class="metric-chart">
            <canvas canvas-id="bodyTempChart" class="mini-chart"></canvas>
          </view>
        </view>
        
        <view class="card-footer">
          <text class="metric-label">{{ bodyTempStatus.text }}</text>
          <text class="metric-range">正常: 36.0-37.3°C</text>
        </view>
      </view>

      <!-- 步数卡片 -->
      <view class="health-card steps">
        <view class="card-header">
          <text class="card-icon">👟</text>
          <text class="card-title">步数</text>
          <view class="steps-progress">
            <text class="progress-text">{{ (healthState.current.steps / 10000 * 100).toFixed(0) }}%</text>
          </view>
        </view>
        
        <view class="card-main">
          <view class="metric-display">
            <text class="metric-value">{{ healthState.current.steps.toLocaleString() }}</text>
            <text class="metric-unit">步</text>
          </view>
          
          <view class="steps-ring">
            <canvas canvas-id="stepsRing" class="ring-chart"></canvas>
            <view class="ring-center">
              <text class="ring-goal">10,000</text>
              <text class="ring-label">目标</text>
            </view>
          </view>
        </view>
        
        <view class="card-footer">
          <text class="metric-label">今日活动</text>
          <text class="metric-range">目标: 10,000步</text>
        </view>
      </view>
    </view>

    <!-- 详细分析选项卡 -->
    <view class="analysis-tabs">
      <view class="tab-item" 
        :class="{ active: activeAnalysisTab === 'trends' }" 
        @tap="switchAnalysisTab('trends')">
        <text class="tab-text">趋势分析</text>
      </view>
      <view class="tab-item" 
        :class="{ active: activeAnalysisTab === 'daily' }" 
        @tap="switchAnalysisTab('daily')">
        <text class="tab-text">每日报告</text>
      </view>
      <view class="tab-item" 
        :class="{ active: activeAnalysisTab === 'advice' }" 
        @tap="switchAnalysisTab('advice')">
        <text class="tab-text">健康建议</text>
      </view>
    </view>

    <!-- 趋势分析 -->
    <view class="analysis-section" v-if="activeAnalysisTab === 'trends'">
      <view class="trend-chart-card">
        <view class="chart-header">
          <text class="chart-title">24小时健康趋势</text>
          <view class="chart-controls">
            <text class="control-btn" 
              :class="{ active: trendPeriod === '24h' }"
              @tap="setTrendPeriod('24h')">24小时</text>
            <text class="control-btn"
              :class="{ active: trendPeriod === '7d' }"
              @tap="setTrendPeriod('7d')">7天</text>
            <text class="control-btn"
              :class="{ active: trendPeriod === '30d' }"
              @tap="setTrendPeriod('30d')">30天</text>
          </view>
        </view>
        
        <view class="chart-container">
          <canvas canvas-id="healthTrendChart" class="trend-chart"></canvas>
        </view>
        
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-color heart"></view>
            <text class="legend-text">心率</text>
          </view>
          <view class="legend-item">
            <view class="legend-color spo2"></view>
            <text class="legend-text">血氧</text>
          </view>
          <view class="legend-item">
            <view class="legend-color temp"></view>
            <text class="legend-text">体温</text>
          </view>
        </view>
      </view>

      <!-- 健康统计 -->
      <view class="health-stats">
        <view class="stats-header">
          <text class="stats-title">健康统计</text>
          <text class="stats-period">{{ trendPeriodText }}</text>
        </view>
        
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-label">平均心率</text>
            <text class="stat-value">{{ healthState.dailyStats.avgHeartRate }} BPM</text>
            <view class="stat-trend up">
              <text class="trend-icon">📈</text>
              <text class="trend-text">+2 BPM</text>
            </view>
          </view>
          
          <view class="stat-item">
            <text class="stat-label">平均血氧</text>
            <text class="stat-value">{{ healthState.dailyStats.avgSpO2 }}%</text>
            <view class="stat-trend stable">
              <text class="trend-icon">➖</text>
              <text class="trend-text">稳定</text>
            </view>
          </view>
          
          <view class="stat-item">
            <text class="stat-label">最高心率</text>
            <text class="stat-value">{{ healthState.dailyStats.maxHeartRate }} BPM</text>
            <view class="stat-trend down">
              <text class="trend-icon">📉</text>
              <text class="trend-text">-5 BPM</text>
            </view>
          </view>
          
          <view class="stat-item">
            <text class="stat-label">最低心率</text>
            <text class="stat-value">{{ healthState.dailyStats.minHeartRate }} BPM</text>
            <view class="stat-trend up">
              <text class="trend-icon">📈</text>
              <text class="trend-text">+3 BPM</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 每日报告 -->
    <view class="analysis-section" v-if="activeAnalysisTab === 'daily'">
      <view class="daily-report">
        <view class="report-header">
          <text class="report-title">今日健康报告</text>
          <text class="report-date">{{ formatDate(Date.now()) }}</text>
        </view>
        
        <view class="report-summary">
          <view class="summary-card" :class="dailyRating.class">
            <text class="summary-icon">{{ dailyRating.icon }}</text>
            <text class="summary-title">{{ dailyRating.title }}</text>
            <text class="summary-desc">{{ dailyRating.description }}</text>
          </view>
        </view>
        
        <view class="report-details">
          <view class="detail-section">
            <text class="section-title">心血管健康</text>
            <view class="detail-content">
              <view class="detail-item">
                <text class="detail-label">静息心率</text>
                <text class="detail-value">{{ healthState.dailyStats.minHeartRate }} BPM</text>
                <view class="detail-status good">
                  <text class="status-text">良好</text>
                </view>
              </view>
              <view class="detail-item">
                <text class="detail-label">心率变异性</text>
                <text class="detail-value">42 ms</text>
                <view class="detail-status good">
                  <text class="status-text">正常</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="detail-section">
            <text class="section-title">呼吸系统</text>
            <view class="detail-content">
              <view class="detail-item">
                <text class="detail-label">血氧饱和度</text>
                <text class="detail-value">{{ healthState.dailyStats.avgSpO2 }}%</text>
                <view class="detail-status excellent">
                  <text class="status-text">优秀</text>
                </view>
              </view>
              <view class="detail-item">
                <text class="detail-label">呼吸频率</text>
                <text class="detail-value">16 次/分</text>
                <view class="detail-status good">
                  <text class="status-text">正常</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="detail-section">
            <text class="section-title">体温调节</text>
            <view class="detail-content">
              <view class="detail-item">
                <text class="detail-label">平均体温</text>
                <text class="detail-value">{{ healthState.current.bodyTemp.toFixed(1) }}°C</text>
                <view class="detail-status good">
                  <text class="status-text">正常</text>
                </view>
              </view>
              <view class="detail-item">
                <text class="detail-label">体温变化</text>
                <text class="detail-value">0.3°C</text>
                <view class="detail-status good">
                  <text class="status-text">稳定</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 健康建议 -->
    <view class="analysis-section" v-if="activeAnalysisTab === 'advice'">
      <view class="health-advice">
        <view class="advice-header">
          <text class="advice-title">个性化健康建议</text>
          <text class="advice-subtitle">基于您的健康数据和辐射暴露情况</text>
        </view>
        
        <view class="advice-categories">
          <view class="advice-category">
            <view class="category-header">
              <text class="category-icon">🏃‍♂️</text>
              <text class="category-title">运动建议</text>
            </view>
            <view class="advice-list">
              <view class="advice-item">
                <text class="advice-text">建议每日进行30分钟中等强度有氧运动</text>
                <view class="advice-priority high">重要</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">当前步数已达成78%，继续加油！</text>
                <view class="advice-priority medium">一般</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">心率较低，可适当增加运动强度</text>
                <view class="advice-priority medium">一般</view>
              </view>
            </view>
          </view>
          
          <view class="advice-category">
            <view class="category-header">
              <text class="category-icon">🛡️</text>
              <text class="category-title">辐射防护</text>
            </view>
            <view class="advice-list">
              <view class="advice-item">
                <text class="advice-text">当前辐射水平安全，继续保持监测</text>
                <view class="advice-priority low">提醒</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">建议补充抗氧化维生素，增强抵抗力</text>
                <view class="advice-priority medium">一般</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">保持充足睡眠，有助于细胞修复</text>
                <view class="advice-priority high">重要</view>
              </view>
            </view>
          </view>
          
          <view class="advice-category">
            <view class="category-header">
              <text class="category-icon">🥗</text>
              <text class="category-title">营养建议</text>
            </view>
            <view class="advice-list">
              <view class="advice-item">
                <text class="advice-text">多摄入富含维生素C和E的食物</text>
                <view class="advice-priority high">重要</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">增加叶酸和β-胡萝卜素的摄入</text>
                <view class="advice-priority medium">一般</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">保持充足的水分摄入，每日2L以上</text>
                <view class="advice-priority medium">一般</view>
              </view>
            </view>
          </view>
          
          <view class="advice-category">
            <view class="category-header">
              <text class="category-icon">😴</text>
              <text class="category-title">睡眠优化</text>
            </view>
            <view class="advice-list">
              <view class="advice-item">
                <text class="advice-text">建议23:00前入睡，保证7-8小时睡眠</text>
                <view class="advice-priority high">重要</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">睡前1小时避免使用电子设备</text>
                <view class="advice-priority medium">一般</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">保持卧室温度在18-22°C</text>
                <view class="advice-priority low">提醒</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 健康数据同步按钮 -->
    <view class="sync-button" @tap="syncHealthData">
      <text class="sync-icon">🔄</text>
      <text class="sync-text">同步健康数据</text>
    </view>

    <!-- 健康提醒设置浮动按钮 -->
    <view class="fab-health" @tap="showHealthSettings">
      <text class="fab-icon">⚙️</text>
    </view>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { healthState, radiationState } from '../../utils/dataStore.js'
import dataStore from '../../utils/dataStore.js'
import ToastContainer from '../../components/ToastContainer.vue'
import toastManager from '../../utils/toastManager.js'

export default {
  name: 'HealthMonitor',
  components: {
    ToastContainer
  },
  setup() {
    const activeAnalysisTab = ref('trends')
    const trendPeriod = ref('24h')
    
    // 图表上下文
    const chartContexts = reactive({
      heartRate: null,
      spO2: null,
      bodyTemp: null,
      steps: null,
      trend: null
    })

    // 计算属性
    const healthScore = computed(() => {
      const { heartRate, spO2, bodyTemp } = healthState.current
      
      // 简单的健康评分算法
      let score = 100
      
      // 心率评分
      if (heartRate < 60 || heartRate > 100) score -= 10
      else if (heartRate >= 70 && heartRate <= 85) score += 5
      
      // 血氧评分
      if (spO2 < 95) score -= 15
      else if (spO2 >= 98) score += 5
      
      // 体温评分
      if (bodyTemp < 36.0 || bodyTemp > 37.3) score -= 8
      else if (bodyTemp >= 36.3 && bodyTemp <= 36.8) score += 3
      
      // 辐射影响评分
      const doseRate = radiationState.currentData.doseRate
      if (doseRate > radiationState.settings.maxDoseRate) score -= 20
      
      return Math.max(Math.min(score, 100), 0)
    })

    const healthStatusClass = computed(() => {
      const score = healthScore.value
      if (score >= 85) return 'excellent'
      if (score >= 70) return 'good'
      if (score >= 55) return 'fair'
      return 'poor'
    })

    const healthStatusIcon = computed(() => {
      const score = healthScore.value
      if (score >= 85) return '🌟'
      if (score >= 70) return '😊'
      if (score >= 55) return '😐'
      return '😟'
    })

    const healthStatusText = computed(() => {
      const score = healthScore.value
      if (score >= 85) return '健康状态优秀'
      if (score >= 70) return '健康状态良好'
      if (score >= 55) return '健康状态一般'
      return '需要关注健康'
    })

    const heartRateStatus = computed(() => {
      const hr = healthState.current.heartRate
      if (hr < 60) return { class: 'low', text: '心率偏低' }
      if (hr > 100) return { class: 'high', text: '心率偏高' }
      if (hr >= 70 && hr <= 85) return { class: 'optimal', text: '心率最佳' }
      return { class: 'normal', text: '心率正常' }
    })

    const spO2Status = computed(() => {
      const spo2 = healthState.current.spO2
      if (spo2 < 90) return { class: 'critical', text: '血氧危险' }
      if (spo2 < 95) return { class: 'low', text: '血氧偏低' }
      if (spo2 >= 98) return { class: 'optimal', text: '血氧优秀' }
      return { class: 'normal', text: '血氧正常' }
    })

    const bodyTempStatus = computed(() => {
      const temp = healthState.current.bodyTemp
      if (temp < 35.0) return { class: 'critical', text: '体温过低' }
      if (temp > 38.0) return { class: 'high', text: '发热' }
      if (temp > 37.3) return { class: 'elevated', text: '体温偏高' }
      if (temp < 36.0) return { class: 'low', text: '体温偏低' }
      return { class: 'normal', text: '体温正常' }
    })

    const trendPeriodText = computed(() => {
      const periodMap = {
        '24h': '过去24小时',
        '7d': '过去7天',
        '30d': '过去30天'
      }
      return periodMap[trendPeriod.value] || '过去24小时'
    })

    const dailyRating = computed(() => {
      const score = healthScore.value
      if (score >= 85) {
        return {
          class: 'excellent',
          icon: '🌟',
          title: '今日状态优秀',
          description: '各项健康指标都很理想，继续保持良好的生活习惯！'
        }
      }
      if (score >= 70) {
        return {
          class: 'good',
          icon: '😊',
          title: '今日状态良好',
          description: '整体健康状况不错，注意保持规律作息和适量运动。'
        }
      }
      if (score >= 55) {
        return {
          class: 'fair',
          icon: '😐',
          title: '今日状态一般',
          description: '部分指标需要改善，建议关注运动和饮食调节。'
        }
      }
      return {
        class: 'poor',
        icon: '😟',
        title: '需要关注健康',
        description: '多项指标异常，建议及时调整生活方式或咨询医生。'
      }
    })

    // 方法
    const switchAnalysisTab = (tab) => {
      activeAnalysisTab.value = tab
      setTimeout(() => {
        if (tab === 'trends') {
          drawHealthTrendChart()
        }
      }, 100)
    }

    const setTrendPeriod = (period) => {
      trendPeriod.value = period
      setTimeout(() => {
        drawHealthTrendChart()
      }, 100)
    }

    const formatDate = (timestamp) => {
      return new Date(timestamp).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }

    const syncHealthData = () => {
      uni.showLoading({
        title: '同步中...'
      })
      
      // 模拟同步延迟
      setTimeout(() => {
        uni.hideLoading()
        toastManager.success('同步成功')
        
        // 更新健康数据
        updateHealthCharts()
      }, 2000)
    }

    const showHealthSettings = () => {
      uni.showActionSheet({
        itemList: ['健康提醒设置', '数据导出', '隐私设置', '关于健康监测'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              toastManager.info('健康提醒设置')
              break
            case 1:
              exportHealthData()
              break
            case 2:
              toastManager.info('隐私设置')
              break
            case 3:
              toastManager.info('关于健康监测')
              break
          }
        }
      })
    }

    const exportHealthData = () => {
      const data = dataStore.exportData('health')
      uni.showModal({
        title: '导出健康数据',
        content: '健康数据已准备完成，您可以将其保存或分享',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            toastManager.success('导出成功')
          }
        }
      })
    }

    // 图表绘制方法
    const initHealthCharts = () => {
      initHeartRateChart()
      initSpO2Chart()
      initBodyTempChart()
      initStepsRing()
      drawHealthTrendChart()
    }

    const initHeartRateChart = () => {
      const ctx = uni.createCanvasContext('heartRateChart')
      if (!ctx) return
      
      chartContexts.heartRate = ctx
      drawMiniChart(ctx, healthState.history.map(item => item.heartRate), '#ff6b6b')
    }

    const initSpO2Chart = () => {
      const ctx = uni.createCanvasContext('spO2Chart')
      if (!ctx) return
      
      chartContexts.spO2 = ctx
      drawMiniChart(ctx, healthState.history.map(item => item.spO2), '#4dabf7')
    }

    const initBodyTempChart = () => {
      const ctx = uni.createCanvasContext('bodyTempChart')
      if (!ctx) return
      
      chartContexts.bodyTemp = ctx
      drawMiniChart(ctx, healthState.history.map(item => item.bodyTemp), '#ffd43b')
    }

    const drawMiniChart = (ctx, data, color) => {
      if (!ctx || !data || data.length < 2) return
      
      const width = 120  // 增加宽度
      const height = 80  // 增加高度
      const padding = 10
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      const recentData = data.slice(-15)  // 显示更多数据点
      
      ctx.clearRect(0, 0, width, height)
      
      // 动态范围计算，增加一些边距
      const max = Math.max(...recentData)
      const min = Math.min(...recentData)
      const baseRange = max - min || 1
      const range = baseRange * 1.2  // 增加20%的显示范围
      const adjustedMin = min - baseRange * 0.1
      
      // 绘制背景渐变
      const gradient = ctx.createLinearGradient(0, 0, 0, height)
      gradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)')
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0.05)')
      ctx.setFillStyle(gradient)
      ctx.fillRect(0, 0, width, height)
      
      // 绘制网格线
      ctx.setStrokeStyle('rgba(255, 255, 255, 0.1)')
      ctx.setLineWidth(0.5)
      
      // 水平网格线
      for (let i = 1; i < 4; i++) {
        const y = padding + (i / 4) * chartHeight
      ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }
      
      // 创建平滑曲线路径
      const points = recentData.map((value, index) => ({
        x: padding + (index / (recentData.length - 1)) * chartWidth,
        y: padding + chartHeight - ((value - adjustedMin) / range) * chartHeight
      }))
      
      // 绘制渐变填充区域
      if (points.length > 1) {
        const fillGradient = ctx.createLinearGradient(0, padding, 0, height - padding)
        fillGradient.addColorStop(0, color.replace('rgb', 'rgba').replace(')', ', 0.3)'))
        fillGradient.addColorStop(1, color.replace('rgb', 'rgba').replace(')', ', 0.05)'))
        ctx.setFillStyle(fillGradient)
        
        ctx.beginPath()
        ctx.moveTo(points[0].x, height - padding)
        points.forEach((point, index) => {
        if (index === 0) {
            ctx.lineTo(point.x, point.y)
        } else {
            // 使用贝塞尔曲线创建平滑效果
            const prevPoint = points[index - 1]
            const cpx = (prevPoint.x + point.x) / 2
            ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
          }
        })
        ctx.lineTo(points[points.length - 1].x, height - padding)
        ctx.closePath()
        ctx.fill()
      }
      
      // 绘制曲线
      ctx.beginPath()
      ctx.setStrokeStyle(color)
      ctx.setLineWidth(3)
      ctx.setShadow(0, 2, 4, color.replace('rgb', 'rgba').replace(')', ', 0.3)'))
      
      points.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          // 平滑曲线
          const prevPoint = points[index - 1]
          const cpx = (prevPoint.x + point.x) / 2
          ctx.quadraticCurveTo(cpx, prevPoint.y, point.x, point.y)
        }
      })
      
      ctx.stroke()
      
      // 绘制数据点
      ctx.setShadow(0, 0, 0, 'transparent')
      points.forEach((point, index) => {
        if (index === points.length - 1) {
          // 最后一个点突出显示
          ctx.beginPath()
          ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI)
          ctx.setFillStyle('#ffffff')
          ctx.fill()
          ctx.beginPath()
          ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI)
          ctx.setFillStyle(color)
          ctx.fill()
        }
      })
      
      ctx.draw()
    }

    const initStepsRing = () => {
      const ctx = uni.createCanvasContext('stepsRing')
      if (!ctx) return
      
      chartContexts.steps = ctx
      drawStepsRing()
    }

    const drawStepsRing = () => {
      if (!chartContexts.steps) return
      
      const ctx = chartContexts.steps
      const width = 120
      const height = 120
      const centerX = width / 2
      const centerY = height / 2
      const radius = 45
      const lineWidth = 8
      
      ctx.clearRect(0, 0, width, height)
      
      // 背景圆环
      ctx.beginPath()
      ctx.setStrokeStyle('rgba(255, 255, 255, 0.1)')
      ctx.setLineWidth(lineWidth)
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
      ctx.stroke()
      
      // 进度圆环
      const progress = Math.min(healthState.current.steps / 10000, 1)
      const endAngle = -Math.PI / 2 + 2 * Math.PI * progress
      
      ctx.beginPath()
      ctx.setStrokeStyle('#3cc51f')
      ctx.setLineWidth(lineWidth)
      ctx.setLineCap('round')
      ctx.arc(centerX, centerY, radius, -Math.PI / 2, endAngle)
      ctx.stroke()
      
      ctx.draw()
    }

    const drawHealthTrendChart = () => {
      const ctx = uni.createCanvasContext('healthTrendChart')
      if (!ctx) return
      
      const width = 300
      const height = 200
      const padding = 30
      
      ctx.clearRect(0, 0, width, height)
      
      // 获取数据
      let timeFilter = 24 * 60 * 60 * 1000 // 24小时
      switch (trendPeriod.value) {
        case '7d': timeFilter = 7 * 24 * 60 * 60 * 1000; break
        case '30d': timeFilter = 30 * 24 * 60 * 60 * 1000; break
      }
      
      const now = Date.now()
      const filteredData = healthState.history.filter(item => 
        (now - item.timestamp) <= timeFilter
      ).slice(-20)
      
      if (filteredData.length < 2) return
      
      // 绘制网格
      ctx.setStrokeStyle('rgba(255, 255, 255, 0.1)')
      ctx.setLineWidth(1)
      for (let i = 1; i < 5; i++) {
        const y = (height - padding * 2) / 4 * i + padding
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }
      
      // 绘制心率曲线
      const heartRates = filteredData.map(item => item.heartRate)
      const maxHR = Math.max(...heartRates)
      const minHR = Math.min(...heartRates)
      const hrRange = maxHR - minHR || 1
      
      ctx.beginPath()
      ctx.setStrokeStyle('#ff6b6b')
      ctx.setLineWidth(2)
      
      filteredData.forEach((item, index) => {
        const x = (index / (filteredData.length - 1)) * (width - padding * 2) + padding
        const y = height - padding - ((item.heartRate - minHR) / hrRange) * (height - padding * 2)
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      ctx.stroke()
      
      ctx.draw()
    }

    const updateHealthCharts = () => {
      if (chartContexts.heartRate) drawMiniChart(chartContexts.heartRate, healthState.history.map(item => item.heartRate), '#ff6b6b')
      if (chartContexts.spO2) drawMiniChart(chartContexts.spO2, healthState.history.map(item => item.spO2), '#4dabf7')
      if (chartContexts.bodyTemp) drawMiniChart(chartContexts.bodyTemp, healthState.history.map(item => item.bodyTemp), '#ffd43b')
      drawStepsRing()
      if (activeAnalysisTab.value === 'trends') {
        drawHealthTrendChart()
      }
    }

    // 生命周期
    onMounted(() => {
      setTimeout(() => {
        initHealthCharts()
      }, 500)

      // 模拟健康数据更新
      const healthDataInterval = setInterval(() => {
        const mockHealthData = {
          heartRate: 70 + Math.round(Math.random() * 20 - 10),
          spO2: 96 + Math.round(Math.random() * 4),
          bodyTemp: 36.5 + (Math.random() * 1.0 - 0.5),
          steps: healthState.current.steps + Math.round(Math.random() * 50)
        }
        
        dataStore.updateHealthData(mockHealthData)
        updateHealthCharts()
      }, 10000) // 每10秒更新一次

      onUnmounted(() => {
        clearInterval(healthDataInterval)
      })
    })

    return {
      activeAnalysisTab,
      trendPeriod,
      healthState,
      healthScore,
      healthStatusClass,
      healthStatusIcon,
      healthStatusText,
      heartRateStatus,
      spO2Status,
      bodyTempStatus,
      trendPeriodText,
      dailyRating,
      switchAnalysisTab,
      setTrendPeriod,
      formatDate,
      syncHealthData,
      showHealthSettings
    }
  }
}
</script>

<style scoped>
.health-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  padding: 24rpx;
}

/* 健康状态总览 - 现代浅色设计 */
.health-overview {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 32rpx;
  padding: 48rpx;
  margin-bottom: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8rpx 32rpx rgba(15, 23, 42, 0.08);
  position: relative;
  overflow: hidden;
}

.health-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #00b4d8, #0096c7, #10b981);
  border-radius: 32rpx 32rpx 0 0;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.overview-title {
  font-size: 40rpx;
  color: #0f172a;
  font-weight: 800;
  background: linear-gradient(135deg, #334155, #1e293b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.health-score {
  text-align: center;
  padding: 20rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 20rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.score-value {
  display: block;
  font-size: 56rpx;
  color: #10b981;
  font-weight: 800;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(16, 185, 129, 0.2);
}

.score-label {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
  margin-top: 8rpx;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 32rpx;
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid;
}

.status-indicator.excellent {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
  border-color: rgba(16, 185, 129, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.1);
}

.status-indicator.good {
  background: linear-gradient(135deg, rgba(0, 180, 216, 0.1), rgba(0, 150, 199, 0.1));
  border-color: rgba(0, 180, 216, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(0, 180, 216, 0.1);
}

.status-indicator.fair {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
  border-color: rgba(245, 158, 11, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.1);
}

.status-indicator.poor {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
  border-color: rgba(239, 68, 68, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.1);
}

.status-icon {
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.status-text {
  font-size: 30rpx;
  color: #0f172a;
  font-weight: 700;
}

/* 健康指标卡片 - 优雅设计 */
.health-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.health-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  position: relative;
  overflow: hidden;
}

.health-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  border-radius: 24rpx 24rpx 0 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.health-card.heart-rate::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.health-card.spo2::before {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.health-card.body-temp::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.health-card.steps::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.health-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(15, 23, 42, 0.12);
  border-color: rgba(203, 213, 225, 0.9);
}

.health-card:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 8rpx 25rpx rgba(15, 23, 42, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.card-title {
  flex: 1;
  font-size: 26rpx;
  color: #0f172a;
  font-weight: 700;
  margin-left: 12rpx;
}

.card-status {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  position: relative;
  box-shadow: 0 0 0 3rpx rgba(255, 255, 255, 0.8), 0 0 0 4rpx currentColor;
}

.card-status.normal {
  background: #10b981;
  color: rgba(16, 185, 129, 0.3);
}

.card-status.optimal {
  background: #00b4d8;
  color: rgba(0, 180, 216, 0.3);
  animation: pulse 2s infinite;
}

.card-status.high,
.card-status.elevated {
  background: #f59e0b;
  color: rgba(245, 158, 11, 0.3);
}

.card-status.low {
  background: #ef4444;
  color: rgba(239, 68, 68, 0.3);
}

.card-status.critical {
  background: #dc2626;
  color: rgba(220, 38, 38, 0.3);
  animation: urgent 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

@keyframes urgent {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

.status-dot {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
}

.card-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.metric-display {
  display: flex;
  align-items: baseline;
  gap: 6rpx;
}

.metric-value {
  font-size: 40rpx;
  color: #0f172a;
  font-weight: 800;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(15, 23, 42, 0.08);
}

.metric-unit {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 600;
}

.metric-chart {
  width: 120rpx;
  height: 80rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.7) 100%);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 
    0 4rpx 12rpx rgba(15, 23, 42, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  overflow: hidden;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.metric-chart:hover {
  transform: scale(1.05);
  box-shadow: 
    0 8rpx 20rpx rgba(15, 23, 42, 0.12),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

.metric-chart::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, rgba(0, 180, 216, 0.6), transparent);
}

.mini-chart {
  width: 100%;
  height: 100%;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
}

.metric-range {
  font-size: 20rpx;
  color: #94a3b8;
  font-weight: 500;
}

/* 步数特殊卡片 */
.health-card.steps .card-main {
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.steps-progress {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
}

.progress-text {
  font-size: 20rpx;
  color: #059669;
  font-weight: 700;
}

.steps-ring {
  position: relative;
  width: 140rpx;
  height: 140rpx;
}

.ring-chart {
  width: 100%;
  height: 100%;
}

.ring-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.ring-goal {
  display: block;
  font-size: 22rpx;
  color: #0f172a;
  font-weight: 700;
  line-height: 1;
}

.ring-label {
  font-size: 18rpx;
  color: #64748b;
  font-weight: 500;
}

/* 分析选项卡 - 现代设计 */
.analysis-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(15, 23, 42, 0.04);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.tab-item.active {
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  box-shadow: 0 4rpx 12rpx rgba(0, 180, 216, 0.3);
}

.tab-text {
  font-size: 26rpx;
  color: #475569;
  font-weight: 600;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #ffffff;
}

/* 分析内容 */
.analysis-section {
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(16rpx); }
  to { opacity: 1; transform: translateY(0); }
}

/* 趋势图表 - 优雅设计 */
.trend-chart-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.chart-title {
  font-size: 30rpx;
  color: #0f172a;
  font-weight: 700;
}

.chart-controls {
  display: flex;
  gap: 12rpx;
}

.control-btn {
  padding: 12rpx 20rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 12rpx;
  color: #64748b;
  font-size: 22rpx;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.control-btn.active {
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  color: #ffffff;
  border-color: rgba(0, 180, 216, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(0, 180, 216, 0.2);
}

.chart-container {
  height: 400rpx;
  margin-bottom: 24rpx;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  overflow: hidden;
}

.trend-chart {
  width: 100%;
  height: 100%;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 32rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.legend-color.heart {
  background: #ef4444;
}

.legend-color.spo2 {
  background: #3b82f6;
}

.legend-color.temp {
  background: #f59e0b;
}

.legend-text {
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
}

/* 健康统计 - 现代卡片 */
.health-stats {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28rpx;
}

.stats-title {
  font-size: 30rpx;
  color: #0f172a;
  font-weight: 700;
}

.stats-period {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.stat-item {
  padding: 24rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(203, 213, 225, 0.8);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.08);
}

.stat-label {
  display: block;
  font-size: 22rpx;
  color: #64748b;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 12rpx;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 600;
  border: 1px solid;
}

.stat-trend.up {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.2);
}

.stat-trend.down {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.2);
}

.stat-trend.stable {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border-color: rgba(245, 158, 11, 0.2);
}

.trend-icon {
  font-size: 16rpx;
}

.trend-text {
  font-size: 18rpx;
  font-weight: 600;
}

/* 每日报告 - 优雅布局 */
.daily-report {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.report-title {
  font-size: 30rpx;
  color: #0f172a;
  font-weight: 700;
}

.report-date {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.report-summary {
  margin-bottom: 32rpx;
}

.summary-card {
  padding: 32rpx;
  border-radius: 24rpx;
  text-align: center;
  border: 1px solid;
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  border-radius: 24rpx 24rpx 0 0;
}

.summary-card.excellent {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(5, 150, 105, 0.05));
  border-color: rgba(16, 185, 129, 0.2);
}

.summary-card.excellent::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.summary-card.good {
  background: linear-gradient(135deg, rgba(0, 180, 216, 0.05), rgba(0, 150, 199, 0.05));
  border-color: rgba(0, 180, 216, 0.2);
}

.summary-card.good::before {
  background: linear-gradient(90deg, #00b4d8, #0096c7);
}

.summary-card.fair {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(217, 119, 6, 0.05));
  border-color: rgba(245, 158, 11, 0.2);
}

.summary-card.fair::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.summary-card.poor {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(220, 38, 38, 0.05));
  border-color: rgba(239, 68, 68, 0.2);
}

.summary-card.poor::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.summary-icon {
  display: block;
  font-size: 56rpx;
  margin-bottom: 16rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.summary-title {
  display: block;
  font-size: 36rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 16rpx;
}

.summary-desc {
  font-size: 26rpx;
  color: #475569;
  line-height: 1.5;
  font-weight: 500;
}

/* 报告详情 */
.report-details {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
}

.detail-section {
  padding: 28rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.section-title {
  display: block;
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 20rpx;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.detail-label {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.detail-value {
  font-size: 26rpx;
  color: #0f172a;
  font-weight: 700;
}

.detail-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  border: 1px solid;
}

.detail-status.excellent {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.2);
}

.detail-status.good {
  background: rgba(0, 180, 216, 0.1);
  color: #0077b6;
  border-color: rgba(0, 180, 216, 0.2);
}

/* 健康建议 - 现代设计 */
.health-advice {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
}

.advice-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.advice-title {
  display: block;
  font-size: 36rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 12rpx;
}

.advice-subtitle {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.5;
  font-weight: 500;
}

.advice-categories {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
}

.advice-category {
  padding: 28rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.category-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.category-title {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
}

.advice-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.advice-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.advice-text {
  flex: 1;
  font-size: 24rpx;
  color: #475569;
  line-height: 1.5;
  font-weight: 500;
}

.advice-priority {
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-size: 18rpx;
  font-weight: 600;
  white-space: nowrap;
  border: 1px solid;
}

.advice-priority.high {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.2);
}

.advice-priority.medium {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border-color: rgba(245, 158, 11, 0.2);
}

.advice-priority.low {
  background: rgba(0, 180, 216, 0.1);
  color: #0077b6;
  border-color: rgba(0, 180, 216, 0.2);
}

/* 同步按钮 */
.sync-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 28rpx;
  margin: 32rpx 0;
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  border: 1px solid rgba(0, 180, 216, 0.3);
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(0, 180, 216, 0.3);
}

.sync-button:active {
  transform: translateY(1rpx) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 180, 216, 0.4);
}

.sync-icon {
  font-size: 32rpx;
  color: #ffffff;
}

.sync-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 700;
}

/* 浮动按钮 */
.fab-health {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 25rpx rgba(139, 92, 246, 0.4);
  z-index: 100;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fab-health:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 15rpx rgba(139, 92, 246, 0.5);
}

.fab-icon {
  font-size: 40rpx;
  color: #ffffff;
}

/* 响应式设计优化 */
@media (max-width: 750rpx) {
  .health-cards {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .health-container {
    padding: 16rpx;
  }
  
  .health-overview {
    padding: 32rpx;
  }
  
  .overview-header {
    flex-direction: column;
    gap: 16rpx;
    text-align: center;
  }
  
  .analysis-tabs {
    flex-direction: column;
    gap: 8rpx;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 16rpx;
    align-items: flex-start;
  }
  
  .advice-item {
    flex-direction: column;
    gap: 12rpx;
    align-items: flex-start;
  }
}
</style> 