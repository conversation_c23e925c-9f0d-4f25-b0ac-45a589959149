<template>
  <view class="map-container">
    <!-- Toast容器 -->
    <ToastContainer />

    <!-- 顶部控制栏 - 现代浅色设计 -->
    <view class="map-header">
      <view class="header-gradient"></view>
      <view class="header-row">
        <view class="location-info">
          <view class="location-icon-wrapper">
            <text class="location-icon">📍</text>
            <view class="location-pulse"></view>
          </view>
          <view class="location-text">
            <text class="current-location">{{ locationState.current ? formatCoordinate(locationState.current.latitude, locationState.current.longitude) : '获取位置中...' }}</text>
            <text class="location-address">{{ locationState.current?.address || '获取地址中...' }}</text>
          </view>
        </view>
        <view class="map-controls">
          <view class="control-btn" :class="{ active: showLayerSelector }" @tap="toggleLayer">
            <text class="control-icon">🗺️</text>
            <text class="control-text">图层</text>
          </view>
          <view class="control-btn" @tap="showLocationInfo">
            <text class="control-icon">📍</text>
            <text class="control-text">位置</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 地图搜索区域 - 优雅设计 -->
    <view class="map-search">
      <view class="search-input-group">
        <text class="control-icon">🔍</text>
        <input type="text" class="search-input" placeholder="搜索地点或地址" v-model="searchQuery" @confirm="searchLocation" />
        <view class="search-btn" @tap="searchLocation">搜索</view>
      </view>
    </view>

    <!-- 主地图区域 - 现代设计 -->
    <view class="map-main">
      <view class="map-content">
      <map 
        id="radiation-map"
          class="map-view"
        :longitude="mapCenter.longitude"
        :latitude="mapCenter.latitude"
        :scale="mapScale"
        :markers="mapMarkers"
        :polyline="mapPolylines"
        :enable-satellite="enableSatellite"
        :enable-traffic="enableTraffic"
        @markertap="onMarkerTap"
        @regionchange="onRegionChange"
        @tap="onMapTap">
        
          <!-- 地图控件 -->
          <cover-view class="map-overlay">
            <view class="map-toolbar">
              <view class="toolbar-group">
                <view class="toolbar-btn" @tap="zoomIn">
                  <text class="control-icon">+</text>
                </view>
                <view class="toolbar-btn" @tap="zoomOut">
                  <text class="control-icon">-</text>
                </view>
              </view>
              
              <view class="toolbar-group">
                <view class="toolbar-btn" @tap="toggleSatellite">
                  <text class="control-icon">🛰️</text>
                </view>
                <view class="toolbar-btn" @tap="toggleTraffic">
                  <text class="control-icon">🚗</text>
                </view>
              </view>
            </view>
          
            <!-- 地图图层控制 -->
            <view class="layer-control">
              <text class="layer-title">地图图层</text>
              <view class="layer-options">
                <view class="layer-option" @tap="toggleHeatmap">
                  <view class="layer-checkbox" :class="{ checked: showHeatmap }"></view>
                  <text class="layer-label">辐射热力图</text>
                  <view class="layer-color" :class="{ radiation: true }"></view>
                </view>
                <view class="layer-option" @tap="toggleTrajectory">
                  <view class="layer-checkbox" :class="{ checked: showTrajectory }"></view>
                  <text class="layer-label">历史轨迹</text>
                  <view class="layer-color" :class="{ radiation: true }"></view>
                </view>
                <view class="layer-option" @tap="toggleAlertPoints">
                  <view class="layer-checkbox" :class="{ checked: showAlertPoints }"></view>
                  <text class="layer-label">报警点位</text>
                  <view class="layer-color" :class="{ radiation: true }"></view>
                </view>
                <view class="layer-option" @tap="toggleMonitoringPoints">
                  <view class="layer-checkbox" :class="{ checked: showMonitoringPoints }"></view>
                  <text class="layer-label">监测点位</text>
                  <view class="layer-color" :class="{ radiation: true }"></view>
                </view>
              </view>
            </view>

        <!-- 实时数据悬浮窗 -->
        <cover-view class="realtime-overlay">
          <cover-view class="realtime-card">
            <cover-view class="realtime-header">
              <cover-view class="status-indicator" :class="radiationLevelClass">
                <cover-view class="indicator-dot"></cover-view>
              </cover-view>
              <cover-view class="realtime-title">实时监测</cover-view>
            </cover-view>
            <cover-view class="realtime-data">
              <cover-view class="data-row">
                <cover-view class="data-label">剂量率</cover-view>
                <cover-view class="data-value">{{ radiationState.currentData.doseRate.toFixed(3) }} μSv/h</cover-view>
              </cover-view>
              <cover-view class="data-row">
                <cover-view class="data-label">位置</cover-view>
                    <cover-view class="data-value">{{ locationState.current ? formatCoordinate(locationState.current.latitude, locationState.current.longitude) : '获取中...' }}</cover-view>
                  </cover-view>
              </cover-view>
            </cover-view>
          </cover-view>
        </cover-view>
      </map>
      </view>
    </view>

    <!-- 地图图层选择器 -->
    <view class="layer-selector" v-if="showLayerSelector">
      <view class="layer-header">
        <text class="layer-title">地图图层</text>
        <text class="layer-close" @tap="hideLayerSelector">✕</text>
      </view>
      <view class="layer-options">
        <view class="layer-option" @tap="toggleHeatmap">
          <text class="option-icon">🔥</text>
          <text class="option-text">辐射热力图</text>
          <view class="option-toggle" :class="{ active: showHeatmap }">
            <view class="toggle-knob"></view>
          </view>
        </view>
        <view class="layer-option" @tap="toggleTrajectory">
          <text class="option-icon">📈</text>
          <text class="option-text">历史轨迹</text>
          <view class="option-toggle" :class="{ active: showTrajectory }">
            <view class="toggle-knob"></view>
          </view>
        </view>
        <view class="layer-option" @tap="toggleAlertPoints">
          <text class="option-icon">⚠️</text>
          <text class="option-text">报警点位</text>
          <view class="option-toggle" :class="{ active: showAlertPoints }">
            <view class="toggle-knob"></view>
          </view>
        </view>
        <view class="layer-option" @tap="toggleMonitoringPoints">
          <text class="option-icon">📡</text>
          <text class="option-text">监测点位</text>
          <view class="option-toggle" :class="{ active: showMonitoringPoints }">
            <view class="toggle-knob"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 位置信息面板 -->
    <view class="location-panel" v-if="showLocationPanel">
      <view class="panel-header">
        <text class="panel-title">位置信息</text>
        <text class="panel-close" @tap="hideLocationPanel">✕</text>
      </view>
      <view class="panel-content">
        <view class="info-group">
          <view class="info-item">
            <text class="info-label">纬度</text>
            <text class="info-value">{{ locationState.current?.latitude?.toFixed(6) || 0 }}°</text>
          </view>
          <view class="info-item">
            <text class="info-label">经度</text>
            <text class="info-value">{{ locationState.current?.longitude?.toFixed(6) || 0 }}°</text>
          </view>
          <view class="info-item">
            <text class="info-label">海拔</text>
            <text class="info-value">{{ locationState.current?.altitude || 0 }} m</text>
          </view>
          <view class="info-item">
            <text class="info-label">精度</text>
            <text class="info-value">± {{ locationState.current?.accuracy || 0 }} m</text>
          </view>
        </view>
        
        <view class="info-group">
          <view class="info-item">
            <text class="info-label">当前剂量率</text>
            <text class="info-value" :class="radiationLevelClass">{{ radiationState.currentData.doseRate.toFixed(3) }} μSv/h</text>
          </view>
          <view class="info-item">
            <text class="info-label">安全评估</text>
            <text class="info-value" :class="radiationLevelClass">{{ radiationStatusText }}</text>
          </view>
        </view>

        <view class="panel-actions">
          <text class="action-btn" @tap="shareLocation">📤 分享位置</text>
          <text class="action-btn" @tap="addPOI">📌 添加标记</text>
        </view>
      </view>
    </view>

    <!-- 大屏报警展示 -->
    <view class="alarm-screen" v-if="showAlarmScreen">
      <view class="alarm-overlay">
        <view class="alarm-content">
          <view class="alarm-header">
            <text class="alarm-title">⚠️ 辐射报警</text>
            <text class="alarm-close" @tap="closeAlarmScreen">✕</text>
          </view>
          
          <view class="alarm-main">
            <view class="alarm-level" :class="currentAlarm?.level">
              <text class="level-text">{{ getAlarmLevelText(currentAlarm?.level) }}</text>
            </view>
            
            <view class="alarm-data">
              <view class="alarm-value">
                <text class="value-number">{{ radiationState.currentData.doseRate.toFixed(3) }}</text>
                <text class="value-unit">μSv/h</text>
              </view>
              
              <view class="alarm-info">
                <text class="alarm-message">{{ currentAlarm?.message }}</text>
                <text class="alarm-time">{{ formatAlarmTime(currentAlarm?.timestamp) }}</text>
              </view>
            </view>
            
            <view class="alarm-location" v-if="currentAlarm?.location">
              <text class="location-title">报警位置</text>
              <text class="location-coords">
                {{ currentAlarm.location.latitude.toFixed(4) }}, {{ currentAlarm.location.longitude.toFixed(4) }}
              </text>
            </view>
          </view>
          
          <view class="alarm-actions">
            <text class="alarm-btn primary" @tap="confirmAlarm">确认报警</text>
            <text class="alarm-btn secondary" @tap="silenceAlarm">静音报警</text>
            <text class="alarm-btn danger" @tap="emergencyCall">紧急呼叫</text>
          </view>
        </view>
        
        <!-- 脉冲动画 -->
        <view class="pulse-animation">
          <view class="pulse-ring"></view>
          <view class="pulse-ring"></view>
          <view class="pulse-ring"></view>
        </view>
      </view>
    </view>

    <!-- 标记详情弹窗 -->
    <view class="marker-popup" v-if="selectedMarker">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">{{ selectedMarker.title }}</text>
          <text class="popup-close" @tap="closeMarkerPopup">✕</text>
        </view>
        <view class="popup-body">
          <view class="popup-info">
            <text class="info-text">{{ selectedMarker.description }}</text>
            <text class="info-time">{{ formatTime(selectedMarker.timestamp) }}</text>
          </view>
          <view class="popup-data" v-if="selectedMarker.data">
            <view class="data-item">
              <text class="data-label">剂量率</text>
              <text class="data-value">{{ selectedMarker.data.doseRate }} μSv/h</text>
            </view>
            <view class="data-item">
              <text class="data-label">计数率</text>
              <text class="data-value">{{ selectedMarker.data.cps }} CPS</text>
            </view>
          </view>
        </view>
        <view class="popup-actions">
          <text class="popup-btn" @tap="navigateToMarker">📍 导航</text>
          <text class="popup-btn" @tap="shareMarker">📤 分享</text>
        </view>
      </view>
    </view>

    <!-- 底部统计栏 -->
    <view class="bottom-stats">
      <view class="stats-item" style="animation-delay: 0.4s">
        <view class="stats-icon">📍</view>
        <view class="stats-content">
          <text class="stats-label">监测点位</text>
          <text class="stats-value">{{ monitoringPointsCount }}</text>
        </view>
      </view>
      <view class="stats-item" style="animation-delay: 0.5s">
        <view class="stats-icon">⚠️</view>
        <view class="stats-content">
          <text class="stats-label">报警点位</text>
          <text class="stats-value">{{ alertPointsCount }}</text>
        </view>
      </view>
      <view class="stats-item" style="animation-delay: 0.6s">
        <view class="stats-icon">📈</view>
        <view class="stats-content">
          <text class="stats-label">轨迹长度</text>
          <text class="stats-value">{{ trajectoryLength.toFixed(1) }}km</text>
        </view>
      </view>
      <view class="stats-item" style="animation-delay: 0.7s">
        <view class="stats-icon">🗺️</view>
        <view class="stats-content">
          <text class="stats-label">覆盖范围</text>
          <text class="stats-value">{{ coverageArea.toFixed(1) }}km²</text>
        </view>
      </view>
    </view>

    <!-- 悬浮按钮 -->
    <view class="fab-map" @tap="centerToLocation">
      <text class="fab-icon">🎯</text>
    </view>

    <!-- 加载状态 -->
    <view class="map-loading" v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 定位精度指示器 -->
    <view class="accuracy-indicator" v-if="locationState.current && locationState.current.accuracy > 0 && locationState.current.accuracy < 1000"></view>

    <!-- 热力图图例 -->
    <view class="heatmap-legend" v-if="showHeatmap">
      <text class="legend-title">辐射热力图</text>
      <view class="legend-gradient"></view>
      <view class="legend-labels">
        <text class="legend-label">低辐射</text>
        <text class="legend-label">高辐射</text>
      </view>
    </view>

    <!-- 距离测量工具 -->
    <view class="distance-info" v-if="showDistanceInfo">
      {{ distanceText }}
    </view>

    <!-- 区域选择工具 -->
    <view class="area-selection" v-if="showAreaSelection"></view>
    <view class="area-info" v-if="showAreaInfo">{{ areaInfoText }}</view>
  </view>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { radiationState, locationState } from '../../utils/dataStore.js'
import ToastContainer from '../../components/ToastContainer.vue'
import toastManager from '../../utils/toastManager.js'

export default {
  name: 'MapView',
  components: {
    ToastContainer
  },
  setup() {
    const mapCenter = ref({
      latitude: 39.9042,
      longitude: 116.4074
    })
    const mapScale = ref(15)
    const showLayerSelector = ref(false)
    const showLocationPanel = ref(false)
    const showAlarmScreen = ref(false)
    const showHeatmap = ref(false)
    const showTrajectory = ref(true)
    const showAlertPoints = ref(true)
    const showMonitoringPoints = ref(true)
    const enableSatellite = ref(false)
    const enableTraffic = ref(false)
    const selectedMarker = ref(null)
    const searchQuery = ref('')
    const isLoading = ref(false)
    const showDistanceInfo = ref(false)
    const distanceText = ref('')
    const showAreaSelection = ref(false)
    const showAreaInfo = ref(false)
    const areaInfoText = ref('')

    // 地图标记和路线
    const mapMarkers = ref([])
    const mapPolylines = ref([])

    // 计算属性
    const radiationLevel = computed(() => {
      const doseRate = radiationState.currentData.doseRate
      const { maxDoseRate, minDoseRate } = radiationState.settings
      
      if (doseRate > maxDoseRate) return 'danger'
      if (doseRate < minDoseRate) return 'warning'
      return 'safe'
    })

    const radiationLevelClass = computed(() => {
      return `level-${radiationLevel.value}`
    })

    const radiationStatusText = computed(() => {
      switch (radiationLevel.value) {
        case 'danger': return '危险区域'
        case 'warning': return '注意区域'
        default: return '安全区域'
      }
    })

    const hasActiveAlarm = computed(() => {
      return radiationState.alerts.length > 0 && 
             radiationState.alerts[0].timestamp > (Date.now() - 300000) // 5分钟内的报警
    })

    const currentAlarm = computed(() => {
      return hasActiveAlarm.value ? radiationState.alerts[0] : null
    })

    const monitoringPointsCount = computed(() => {
      return mapMarkers.value.filter(marker => marker.type === 'monitoring').length
    })

    const alertPointsCount = computed(() => {
      return mapMarkers.value.filter(marker => marker.type === 'alert').length
    })

    const trajectoryLength = computed(() => {
      if (locationState.history.length < 2) return 0
      
      let totalDistance = 0
      for (let i = 1; i < locationState.history.length; i++) {
        const prev = locationState.history[i - 1]
        const curr = locationState.history[i]
        totalDistance += calculateDistance(prev.latitude, prev.longitude, curr.latitude, curr.longitude)
      }
      return totalDistance
    })

    const coverageArea = computed(() => {
      if (locationState.history.length < 3) return 0
      
      // 简单的包围盒面积计算
      const lats = locationState.history.map(point => point.latitude)
      const lngs = locationState.history.map(point => point.longitude)
      
      const latRange = Math.max(...lats) - Math.min(...lats)
      const lngRange = Math.max(...lngs) - Math.min(...lngs)
      
      // 近似计算（1度约111km）
      return latRange * lngRange * 111 * 111
    })

    // 监听报警状态变化，自动显示报警屏幕
    watch(hasActiveAlarm, (newValue, oldValue) => {
      if (newValue && !oldValue) {
        // 有新的报警时自动显示报警屏幕
        openAlarmScreen()
      }
    })

    // 方法
    const toggleLayer = () => {
      showLayerSelector.value = !showLayerSelector.value
    }

    const hideLayerSelector = () => {
      showLayerSelector.value = false
    }

    const showLocationInfo = () => {
      showLocationPanel.value = true
    }

    const hideLocationPanel = () => {
      showLocationPanel.value = false
    }

    const openAlarmScreen = () => {
      showAlarmScreen.value = true
    }

    const closeAlarmScreen = () => {
      showAlarmScreen.value = false
    }

    const centerToLocation = () => {
      if (!locationState.current) {
        toastManager.warning('位置信息获取中...')
        return
      }
      mapCenter.value = {
        latitude: locationState.current.latitude,
        longitude: locationState.current.longitude
      }
      toastManager.success('已定位到当前位置')
    }

    const zoomIn = () => {
      if (mapScale.value < 18) {
        mapScale.value += 1
      }
    }

    const zoomOut = () => {
      if (mapScale.value > 3) {
        mapScale.value -= 1
      }
    }

    const toggleSatellite = () => {
      enableSatellite.value = !enableSatellite.value
    }

    const toggleTraffic = () => {
      enableTraffic.value = !enableTraffic.value
    }

    const toggleHeatmap = () => {
      showHeatmap.value = !showHeatmap.value
      updateMapLayers()
    }

    const toggleTrajectory = () => {
      showTrajectory.value = !showTrajectory.value
      updateMapLayers()
    }

    const toggleAlertPoints = () => {
      showAlertPoints.value = !showAlertPoints.value
      updateMapMarkers()
    }

    const toggleMonitoringPoints = () => {
      showMonitoringPoints.value = !showMonitoringPoints.value
      updateMapMarkers()
    }

    const updateMapMarkers = () => {
      const markers = []

      // 当前位置标记
      if (locationState.current) {
      markers.push({
        id: 'current',
        latitude: locationState.current.latitude,
        longitude: locationState.current.longitude,
        iconPath: '/static/icons/current-location.png',
        width: 40,
        height: 40,
        title: '当前位置',
        type: 'current',
        data: radiationState.currentData
      })
      }

      // 监测点位标记
      if (showMonitoringPoints.value) {
        radiationState.history.forEach((point, index) => {
          if (index % 10 === 0) { // 每10个点显示一个标记
            markers.push({
              id: `monitoring-${index}`,
              latitude: locationState.history[Math.min(index, locationState.history.length - 1)]?.latitude || point.latitude || locationState.current?.latitude || 0,
              longitude: locationState.history[Math.min(index, locationState.history.length - 1)]?.longitude || point.longitude || locationState.current?.longitude || 0,
              iconPath: '/static/icons/monitoring-point.png',
              width: 30,
              height: 30,
              title: '监测点',
              type: 'monitoring',
              data: point,
              timestamp: point.timestamp
            })
          }
        })
      }

      // 报警点位标记
      if (showAlertPoints.value) {
        radiationState.alerts.forEach((alert, index) => {
          if (alert.location) {
            markers.push({
              id: `alert-${index}`,
              latitude: alert.location.latitude,
              longitude: alert.location.longitude,
              iconPath: '/static/icons/alert-point.png',
              width: 35,
              height: 35,
              title: '报警点位',
              type: 'alert',
              data: {
                doseRate: alert.doseRate,
                doseSum: alert.doseSum
              },
              timestamp: alert.timestamp,
              description: alert.message
            })
          }
        })
      }

      mapMarkers.value = markers
    }

    const updateMapLayers = () => {
      const polylines = []

      // 历史轨迹
      if (showTrajectory.value && locationState.history.length > 1) {
        const points = locationState.history.slice(-50).map(point => ({
          latitude: point.latitude,
          longitude: point.longitude
        }))

        polylines.push({
          points,
          color: '#3cc51f',
          width: 4,
          dottedLine: false,
          arrowLine: true
        })
      }

      mapPolylines.value = polylines
    }

    const onMarkerTap = (e) => {
      const markerId = e.detail.markerId
      const marker = mapMarkers.value.find(m => m.id === markerId)
      if (marker) {
        selectedMarker.value = marker
      }
    }

    const closeMarkerPopup = () => {
      selectedMarker.value = null
    }

    const onRegionChange = (e) => {
      if (e.type === 'end') {
        mapCenter.value = {
          latitude: e.detail.centerLocation.latitude,
          longitude: e.detail.centerLocation.longitude
        }
      }
    }

    const onMapTap = (e) => {
      // 点击地图空白区域时关闭弹窗
      selectedMarker.value = null
      showLayerSelector.value = false
      showLocationPanel.value = false
    }

    const formatCoordinate = (lat, lng) => {
      return `${lat.toFixed(4)}, ${lng.toFixed(4)}`
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const formatAlarmTime = (timestamp) => {
      const now = Date.now()
      const diff = now - timestamp
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return new Date(timestamp).toLocaleDateString('zh-CN')
    }

    const getAlarmLevelText = (level) => {
      const levelMap = {
        'error': '严重报警',
        'warning': '警告报警',
        'info': '提示报警'
      }
      return levelMap[level] || '未知报警'
    }

    const calculateDistance = (lat1, lng1, lat2, lng2) => {
      const R = 6371 // 地球半径（公里）
      const dLat = (lat2 - lat1) * Math.PI / 180
      const dLng = (lng2 - lng1) * Math.PI / 180
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
      return R * c
    }

    const shareLocation = () => {
      const location = locationState.current
      const message = `我的位置：${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}\n当前辐射水平：${radiationState.currentData.doseRate.toFixed(3)} μSv/h`
      
      uni.showModal({
        title: '分享位置',
        content: message,
        confirmText: '复制',
        success: (res) => {
          if (res.confirm) {
            uni.setClipboardData({
              data: message,
              success: () => {
                toastManager.success('已复制到剪贴板')
              }
            })
          }
        }
      })
    }

    const addPOI = () => {
      uni.showModal({
        title: '添加标记',
        content: '在当前位置添加自定义标记点',
        success: (res) => {
          if (res.confirm && locationState.current) {
            // 添加POI逻辑
            locationState.poi.push({
              latitude: locationState.current.latitude,
              longitude: locationState.current.longitude,
              name: `POI-${Date.now()}`,
              timestamp: Date.now(),
              radiationData: {...radiationState.currentData}
            })
            
            updateMapMarkers()

            toastManager.success('标记已添加')
          }
        }
      })
    }

    const confirmAlarm = () => {
      toastManager.success('报警已确认')
      closeAlarmScreen()
    }

    const silenceAlarm = () => {
      toastManager.success('报警已静音')
      closeAlarmScreen()
    }

    const emergencyCall = () => {
      uni.showModal({
        title: '紧急呼叫',
        content: '是否拨打紧急电话？',
        confirmText: '拨打',
        success: (res) => {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: '120' // 紧急电话
            })
          }
        }
      })
    }

    const navigateToMarker = () => {
      if (selectedMarker.value) {
        const marker = selectedMarker.value
        uni.openLocation({
          latitude: marker.latitude,
          longitude: marker.longitude,
          name: marker.title,
          scale: 18
        })
      }
    }

    const shareMarker = () => {
      if (selectedMarker.value) {
        const marker = selectedMarker.value
        const message = `${marker.title}\n位置：${marker.latitude.toFixed(6)}, ${marker.longitude.toFixed(6)}`
        
        uni.setClipboardData({
          data: message,
          success: () => {
            toastManager.success('标记信息已复制')
          }
        })
      }
    }

    const searchLocation = () => {
      if (!searchQuery.value) return
      isLoading.value = true
      uniCloud.callFunction({
        name: 'search-location',
        data: {
          query: searchQuery.value
        }
      }).then(res => {
        const result = res.result.data
        if (result && result.latitude && result.longitude) {
          mapCenter.value = {
            latitude: result.latitude,
            longitude: result.longitude
          }
          toastManager.success(`已定位到: ${result.name}`)
        } else {
          toastManager.warning('未找到位置')
        }
      }).catch(err => {
        toastManager.error('搜索失败')
        console.error(err)
      }).finally(() => {
        isLoading.value = false
      })
    }

    const showDistance = (e) => {
      if (e.type === 'markertap') {
        const markerId = e.detail.markerId
        const marker = mapMarkers.value.find(m => m.id === markerId)
        if (marker && locationState.current) {
          const start = { latitude: locationState.current.latitude, longitude: locationState.current.longitude }
          const end = { latitude: marker.latitude, longitude: marker.longitude }
          const distance = calculateDistance(start.latitude, start.longitude, end.latitude, end.longitude)
          distanceText.value = `${distance.toFixed(2)} km`
          showDistanceInfo.value = true
          toastManager.info(`距离: ${distanceText.value}`)
        }
      } else if (e.type === 'regionchange') {
        showDistanceInfo.value = false
      }
    }

    const showArea = (e) => {
      if (e.type === 'regionchange') {
        showAreaSelection.value = true
        showAreaInfo.value = false
        areaInfoText.value = ''
      }
    }

    const hideAreaSelection = () => {
      showAreaSelection.value = false
    }

    const getAreaInfo = (e) => {
      if (e.type === 'regionchange') {
        const bounds = e.detail.bounds
        const lat1 = bounds.southwest.latitude
        const lng1 = bounds.southwest.longitude
        const lat2 = bounds.northeast.latitude
        const lng2 = bounds.northeast.longitude

        const area = Math.abs(calculateDistance(lat1, lng1, lat1, lng2) * calculateDistance(lat1, lng2, lat2, lng2))
        areaInfoText.value = `区域面积: ${area.toFixed(2)} km²`
        showAreaInfo.value = true
        hideAreaSelection()
      }
    }

    // 生命周期
    onMounted(() => {
      // 初始化地图中心为当前位置
      if (locationState.current) {
      mapCenter.value = {
        latitude: locationState.current.latitude,
        longitude: locationState.current.longitude
        }
      }

      // 更新地图标记和图层
      updateMapMarkers()
      updateMapLayers()

      // 监听位置变化
      const locationWatcher = setInterval(() => {
        updateMapMarkers()
        updateMapLayers()
      }, 10000) // 每10秒更新一次

      onUnmounted(() => {
        clearInterval(locationWatcher)
      })
    })

    return {
      mapCenter,
      mapScale,
      showLayerSelector,
      showLocationPanel,
      showAlarmScreen,
      showHeatmap,
      showTrajectory,
      showAlertPoints,
      showMonitoringPoints,
      enableSatellite,
      enableTraffic,
      selectedMarker,
      mapMarkers,
      mapPolylines,
      radiationState,
      locationState,
      radiationLevelClass,
      radiationStatusText,
      hasActiveAlarm,
      currentAlarm,
      monitoringPointsCount,
      alertPointsCount,
      trajectoryLength,
      coverageArea,
      toggleLayer,
      hideLayerSelector,
      showLocationInfo,
      hideLocationPanel,
      openAlarmScreen,
      closeAlarmScreen,
      centerToLocation,
      zoomIn,
      zoomOut,
      toggleSatellite,
      toggleTraffic,
      toggleHeatmap,
      toggleTrajectory,
      toggleAlertPoints,
      toggleMonitoringPoints,
      onMarkerTap,
      closeMarkerPopup,
      onRegionChange,
      onMapTap,
      formatCoordinate,
      formatTime,
      formatAlarmTime,
      getAlarmLevelText,
      shareLocation,
      addPOI,
      confirmAlarm,
      silenceAlarm,
      emergencyCall,
      navigateToMarker,
      shareMarker,
      searchQuery,
      searchLocation,
      showDistance,
      showArea,
      hideAreaSelection,
      getAreaInfo,
      isLoading,
      showDistanceInfo,
      distanceText,
      showAreaSelection,
      showAreaInfo,
      areaInfoText
    }
  }
}
</script>

<style scoped>
.map-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  padding: 24rpx;
  position: relative;
}

.map-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 20%, rgba(0, 180, 216, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* 顶部控制栏 - 现代浅色设计 */
.map-header {
  position: relative;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8rpx 32rpx rgba(15, 23, 42, 0.08);
  overflow: hidden;
  animation: slideInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #00b4d8, #0096c7, #10b981, #06ffa5);
  border-radius: 24rpx 24rpx 0 0;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background: linear-gradient(90deg, #00b4d8, #0096c7, #10b981, #06ffa5); }
  50% { background: linear-gradient(90deg, #06ffa5, #10b981, #0096c7, #00b4d8); }
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16rpx;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.location-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-icon {
  font-size: 28rpx;
  color: #00b4d8;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 180, 216, 0.2));
  animation: locationPulse 2s ease-in-out infinite;
}

.location-pulse {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: rgba(0, 180, 216, 0.2);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes locationPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.location-text {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.current-location {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 700;
}

.location-address {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.map-controls {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: wrap;
}

.control-btn {
  padding: 12rpx 20rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 12rpx;
  color: #64748b;
  font-size: 20rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 8rpx;
  cursor: pointer;
}

.control-btn:hover {
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  color: #ffffff;
  border-color: rgba(0, 180, 216, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(0, 180, 216, 0.2);
}

.control-btn.active {
  background: linear-gradient(135deg, #10b981, #059669);
  color: #ffffff;
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);
}

.control-icon {
  font-size: 18rpx;
}

/* 地图搜索区域 - 优雅设计 */
.map-search {
  position: relative;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  overflow: hidden;
  animation: slideInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both;
}

.map-search::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
  border-radius: 20rpx 20rpx 0 0;
}

.search-input-group {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.search-input {
  flex: 1;
  padding: 16rpx 20rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 0 0 0 3rpx rgba(139, 92, 246, 0.1);
}

.search-btn {
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 16rpx;
  color: #ffffff;
  font-size: 18rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(139, 92, 246, 0.3);
}

.search-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.4);
}

/* 主地图区域 - 现代设计 */
.map-main {
  position: relative;
  z-index: 5;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  overflow: hidden;
  animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.map-main::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #00b4d8, #0096c7, #10b981);
  border-radius: 24rpx 24rpx 0 0;
  z-index: 2;
}

.map-content {
  height: 800rpx;
  position: relative;
}

.map-view {
  width: 100%;
  height: 100%;
  border-radius: 0 0 24rpx 24rpx;
}

.map-overlay {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  right: 20rpx;
  bottom: 20rpx;
  pointer-events: none;
  z-index: 6;
}

/* 地图工具栏 - 浮动设计 */
.map-toolbar {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  pointer-events: all;
  animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
}

.toolbar-group {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16rpx;
  padding: 12rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.08);
}

.toolbar-btn {
  width: 64rpx;
  height: 64rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-size: 24rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  margin-bottom: 8rpx;
}

.toolbar-btn:last-child {
  margin-bottom: 0;
}

.toolbar-btn:hover {
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  color: #ffffff;
  border-color: rgba(0, 180, 216, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(0, 180, 216, 0.2);
  transform: scale(1.05);
}

.toolbar-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 180, 216, 0.3);
}

.toolbar-btn.active {
  background: linear-gradient(135deg, #10b981, #059669);
  color: #ffffff;
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);
}

/* 地图图层控制 */
.layer-control {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16rpx;
  padding: 20rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.08);
  pointer-events: all;
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
}

.layer-title {
  font-size: 22rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 16rpx;
}

.layer-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.layer-option {
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
}

.layer-checkbox {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #94a3b8;
  border-radius: 4rpx;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.layer-checkbox.checked {
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  border-color: #00b4d8;
}

.layer-checkbox.checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-size: 14rpx;
  font-weight: 700;
}

.layer-label {
  font-size: 20rpx;
  color: #475569;
  font-weight: 500;
}

.layer-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-left: auto;
}

.layer-color.radiation {
  background: #06ffa5;
}

.layer-color.temperature {
  background: #f59e0b;
}

.layer-color.humidity {
  background: #3b82f6;
}

.layer-color.air-quality {
  background: #8b5cf6;
}

/* 设备标记点 - 优雅设计 */
.device-marker {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border: 3rpx solid;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 700;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.device-marker.online {
  border-color: #10b981;
  color: #059669;
}

.device-marker.offline {
  border-color: #94a3b8;
  color: #64748b;
}

.device-marker.warning {
  border-color: #f59e0b;
  color: #d97706;
}

.device-marker.critical {
  border-color: #ef4444;
  color: #dc2626;
  animation: pulse-critical 2s infinite;
}

@keyframes pulse-critical {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15), 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  50% { 
    transform: scale(1.1);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15), 0 0 0 8rpx rgba(239, 68, 68, 0);
  }
}

.device-marker:hover {
  transform: scale(1.2);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.device-marker::after {
  content: '';
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.device-marker.has-alert::after {
  opacity: 1;
  background: #ef4444;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.3);
  animation: pulse 2s infinite;
}

/* 信息窗口 - 现代卡片设计 */
.info-window {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8rpx 32rpx rgba(15, 23, 42, 0.15);
  min-width: 300rpx;
  max-width: 400rpx;
  position: relative;
  overflow: hidden;
}

.info-window::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #00b4d8, #0096c7);
  border-radius: 20rpx 20rpx 0 0;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.device-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 18rpx;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(0, 180, 216, 0.3);
}

.device-details {
  flex: 1;
}

.device-name {
  font-size: 22rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 4rpx;
}

.device-type {
  font-size: 18rpx;
  color: #64748b;
  font-weight: 500;
}

.device-status {
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-size: 16rpx;
  font-weight: 600;
  border: 1px solid;
}

.device-status.online {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.2);
}

.device-status.offline {
  background: rgba(148, 163, 184, 0.1);
  color: #64748b;
  border-color: rgba(148, 163, 184, 0.2);
}

.info-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.metric-item {
  padding: 16rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 12rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  text-align: center;
}

.metric-value {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 4rpx;
}

.metric-label {
  font-size: 16rpx;
  color: #64748b;
  font-weight: 500;
}

.metric-unit {
  font-size: 16rpx;
  color: #94a3b8;
  font-weight: 500;
  margin-left: 2rpx;
}

.info-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  flex: 1;
  padding: 12rpx 16rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  cursor: pointer;
  border: 1px solid;
}

.action-btn.primary {
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  color: #ffffff;
  border-color: rgba(0, 180, 216, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(0, 180, 216, 0.2);
}

.action-btn.secondary {
  background: rgba(248, 250, 252, 0.8);
  color: #64748b;
  border-color: rgba(226, 232, 240, 0.6);
}

.action-btn:active {
  transform: scale(0.98);
}

/* 底部统计信息 - 现代卡片 */
.map-stats {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 28rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  border-radius: 20rpx 20rpx 0 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card.devices::before {
  background: linear-gradient(90deg, #00b4d8, #0096c7);
}

.stat-card.alerts::before {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.stat-card.coverage::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.stat-card.quality::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.stat-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(15, 23, 42, 0.12);
  border-color: rgba(203, 213, 225, 0.9);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.stat-icon {
  font-size: 24rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.stat-label {
  font-size: 20rpx;
  color: #0f172a;
  font-weight: 700;
  margin-left: 8rpx;
}

.stat-trend {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 14rpx;
  font-weight: 600;
  border: 1px solid;
}

.stat-trend.up {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.2);
}

.stat-trend.down {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.2);
}

.stat-trend.stable {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border-color: rgba(245, 158, 11, 0.2);
}

.stat-value {
  font-size: 36rpx;
  color: #0f172a;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(15, 23, 42, 0.08);
}

.stat-desc {
  font-size: 18rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.3;
}

/* 底部统计栏 */
.bottom-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160rpx, 1fr));
  gap: 16rpx;
  margin-bottom: 24rpx;
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

.stats-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  display: flex;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: bounceIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.stats-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(15, 23, 42, 0.12);
}

.stats-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.stats-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.stats-label {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.stats-value {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
}

/* 悬浮按钮 */
.fab-map {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #00b4d8, #0096c7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 25rpx rgba(0, 180, 216, 0.4);
  z-index: 100;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: bounceIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.8s both;
}

.fab-map:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 15rpx rgba(0, 180, 216, 0.5);
}

.fab-icon {
  font-size: 40rpx;
  color: #ffffff;
}

/* 响应式设计优化 */
@media (max-width: 750rpx) {
  .map-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
}

  .map-container {
    padding: 16rpx;
  }
  
  .map-header {
    padding: 24rpx;
}

  .header-row {
    flex-direction: column;
    align-items: stretch;
    gap: 16rpx;
  }
  
  .map-controls {
  justify-content: center;
}

  .map-content {
    height: 600rpx;
}

  .map-toolbar {
    right: 16rpx;
    top: 16rpx;
}

  .layer-control {
    left: 16rpx;
    top: 16rpx;
}

  .toolbar-btn {
    width: 56rpx;
    height: 56rpx;
    font-size: 20rpx;
}

  .info-window {
    min-width: 280rpx;
    max-width: 320rpx;
  }
  
  .info-metrics {
    grid-template-columns: 1fr;
    gap: 12rpx;
}

  .info-actions {
    flex-direction: column;
    gap: 8rpx;
  }
  
  .current-location {
    font-size: 20rpx;
}

  .location-address {
    font-size: 18rpx;
}

  .stat-value {
    font-size: 28rpx;
}
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(16rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInDown {
  from { opacity: 0; transform: translateY(-30rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInUp {
  from { opacity: 0; transform: translateY(30rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-30rpx); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(30rpx); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.3); }
  50% { opacity: 1; transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}

.fade-in {
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-down {
  animation: slideInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-up {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-left {
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-right {
  animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in {
  animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 加载状态 */
.map-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  z-index: 10;
}

.loading-spinner {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid rgba(0, 180, 216, 0.2);
  border-top: 4rpx solid #00b4d8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

/* 定位精度指示器 */
.accuracy-indicator {
  position: absolute;
  border-radius: 50%;
  border: 2rpx solid rgba(0, 180, 216, 0.3);
  background: rgba(0, 180, 216, 0.1);
  pointer-events: none;
}

/* 热力图图例 */
.heatmap-legend {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16rpx;
  padding: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.08);
  pointer-events: all;
}

.legend-title {
  font-size: 18rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 12rpx;
}

.legend-gradient {
  width: 150rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background: linear-gradient(90deg, #06ffa5, #f59e0b, #ef4444);
  margin-bottom: 8rpx;
}

.legend-labels {
  display: flex;
  justify-content: space-between;
}

.legend-label {
  font-size: 14rpx;
  color: #64748b;
  font-weight: 500;
}

/* 距离测量工具 */
.distance-info {
  position: absolute;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12rpx;
  padding: 8rpx 12rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(15, 23, 42, 0.08);
  font-size: 16rpx;
  color: #0f172a;
  font-weight: 600;
  pointer-events: none;
}

/* 区域选择工具 */
.area-selection {
  position: absolute;
  border: 2rpx dashed #00b4d8;
  background: rgba(0, 180, 216, 0.1);
  pointer-events: none;
}

.area-info {
  position: absolute;
  top: -40rpx;
  left: 0;
  background: rgba(0, 180, 216, 0.9);
  color: #ffffff;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 14rpx;
  font-weight: 600;
  white-space: nowrap;
}
</style> 