<template>
  <view class="settings-container">
    <!-- Toast容器 -->
    <ToastContainer />

    <!-- 设置头部 -->
    <view class="settings-header">
      <view class="header-gradient"></view>
      <view class="header-content">
        <view class="header-left">
          <view class="header-icon-wrapper">
            <text class="header-icon">⚙️</text>
            <view class="icon-glow"></view>
          </view>
          <view class="header-text">
            <text class="header-title">设置中心</text>
            <text class="header-subtitle">个性化您的应用体验</text>
          </view>
        </view>
        <view class="header-actions">
          <view class="action-btn" @tap="resetAllSettings">
            <text class="btn-icon">🔄</text>
            <text class="btn-text">重置</text>
          </view>
          <view class="action-btn save" @tap="saveAllSettings">
            <text class="btn-icon">💾</text>
            <text class="btn-text">保存</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 设置分类 -->
    <scroll-view class="settings-content" scroll-y>
      <!-- 辐射监测设置 -->
      <view class="settings-section" style="animation-delay: 0.1s">
        <view class="section-header">
          <text class="section-icon">☢️</text>
          <text class="section-title">辐射监测设置</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">剂量率报警阈值</text>
              <text class="item-desc">超过此值将触发高剂量率报警</text>
            </view>
            <view class="item-control">
              <input 
                class="threshold-input" 
                type="digit" 
                v-model="settings.radiation.maxDoseRate"
                placeholder="1.0" />
              <text class="threshold-unit">μSv/h</text>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">最低剂量率阈值</text>
              <text class="item-desc">低于此值将触发低剂量率警告</text>
            </view>
            <view class="item-control">
              <input 
                class="threshold-input" 
                type="digit" 
                v-model="settings.radiation.minDoseRate"
                placeholder="0.01" />
              <text class="threshold-unit">μSv/h</text>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">累积剂量报警阈值</text>
              <text class="item-desc">累积剂量超过此值将触发报警</text>
            </view>
            <view class="item-control">
              <input 
                class="threshold-input" 
                type="digit" 
                v-model="settings.radiation.maxDoseSum"
                placeholder="100.0" />
              <text class="threshold-unit">μSv</text>
            </view>
          </view>
          
          <view class="setting-item action-item" @tap="resetDoseSum">
            <view class="item-info">
              <text class="item-title">重置累积剂量</text>
              <text class="item-desc">将累积剂量清零重新开始计算</text>
            </view>
            <view class="item-control">
              <text class="storage-size">当前: {{ radiationState.currentData.doseSum.toFixed(6) }} μSv</text>
              <text class="action-btn danger">重置</text>
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">自动数据上传</text>
              <text class="item-desc">自动将监测数据上传到服务器</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.radiation.autoUpload" 
                @change="toggleAutoUpload" 
                color="#3cc51f" />
            </view>
          </view>
          
          <view class="setting-item" v-if="settings.radiation.autoUpload">
            <view class="item-info">
              <text class="item-title">上传间隔</text>
              <text class="item-desc">自动上传数据的时间间隔</text>
            </view>
            <view class="item-control">
              <picker 
                mode="selector"
                :range="uploadIntervals"
                :value="uploadIntervalIndex"
                @change="onUploadIntervalChange">
                <view class="picker-display">{{ uploadIntervals[uploadIntervalIndex] }}</view>
              </picker>
            </view>
          </view>
        </view>
      </view>

      <!-- MQTT服务器设置 -->
      <view class="settings-section" style="animation-delay: 0.2s">
        <view class="section-header">
          <text class="section-icon">📡</text>
          <text class="section-title">MQTT服务器设置</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">服务器地址</text>
              <text class="item-desc">MQTT代理服务器的地址</text>
            </view>
            <view class="item-control">
              <input 
                class="text-input" 
                type="text" 
                v-model="settings.mqtt.host"
                placeholder="broker.emqx.io" />
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">端口号</text>
              <text class="item-desc">MQTT服务器端口</text>
            </view>
            <view class="item-control">
              <input 
                class="text-input" 
                type="number" 
                v-model="settings.mqtt.port"
                placeholder="8083" />
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">用户名</text>
              <text class="item-desc">MQTT连接用户名（可选）</text>
            </view>
            <view class="item-control">
              <input 
                class="text-input" 
                type="text" 
                v-model="settings.mqtt.username"
                placeholder="用户名" />
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">密码</text>
              <text class="item-desc">MQTT连接密码（可选）</text>
            </view>
            <view class="item-control">
              <input 
                class="text-input" 
                type="password" 
                v-model="settings.mqtt.password"
                placeholder="密码" />
            </view>
          </view>
          
          <view class="setting-item action-item">
            <view class="item-info">
              <text class="item-title">连接测试</text>
              <text class="item-desc">测试MQTT服务器连接</text>
            </view>
            <view class="item-control">
              <text class="test-btn" @tap="testMqttConnection">
                {{ mqttTestStatus }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 通知和提醒设置 -->
      <view class="settings-section" style="animation-delay: 0.3s">
        <view class="section-header">
          <text class="section-icon">🔔</text>
          <text class="section-title">通知和提醒</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">声音报警</text>
              <text class="item-desc">触发报警时播放声音</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.notifications.soundAlert" 
                @change="toggleSoundAlert" 
                color="#3cc51f" />
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">震动报警</text>
              <text class="item-desc">触发报警时设备震动</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.notifications.vibrationAlert" 
                @change="toggleVibrationAlert" 
                color="#3cc51f" />
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">健康提醒</text>
              <text class="item-desc">定时提醒健康监测</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.notifications.healthReminder" 
                @change="toggleHealthReminder" 
                color="#3cc51f" />
            </view>
          </view>
          
          <view class="setting-item" v-if="settings.notifications.healthReminder">
            <view class="item-info">
              <text class="item-title">提醒间隔</text>
              <text class="item-desc">健康提醒的时间间隔</text>
            </view>
            <view class="item-control">
              <picker 
                mode="selector"
                :range="reminderIntervals"
                :value="reminderIntervalIndex"
                @change="onReminderIntervalChange">
                <view class="picker-display">{{ reminderIntervals[reminderIntervalIndex] }}</view>
              </picker>
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">数据异常通知</text>
              <text class="item-desc">数据异常时发送通知</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.notifications.dataAnomalyAlert" 
                @change="toggleDataAnomalyAlert" 
                color="#3cc51f" />
            </view>
          </view>
        </view>
      </view>

      <!-- 显示和主题设置 -->
      <view class="settings-section" style="animation-delay: 0.4s">
        <view class="section-header">
          <text class="section-icon">🎨</text>
          <text class="section-title">显示和主题</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">主题模式</text>
              <text class="item-desc">选择应用的主题颜色</text>
            </view>
            <view class="item-control">
              <picker 
                mode="selector"
                :range="themes"
                :value="themeIndex"
                @change="onThemeChange">
                <view class="picker-display">{{ themes[themeIndex] }}</view>
              </picker>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">字体大小</text>
              <text class="item-desc">调整应用字体大小</text>
            </view>
            <view class="item-control">
              <picker 
                mode="selector"
                :range="fontSizes"
                :value="fontSizeIndex"
                @change="onFontSizeChange">
                <view class="picker-display">{{ fontSizes[fontSizeIndex] }}</view>
              </picker>
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">保持屏幕常亮</text>
              <text class="item-desc">监测时保持屏幕不熄灭</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.display.keepScreenOn" 
                @change="toggleKeepScreenOn" 
                color="#3cc51f" />
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">显示实时数据</text>
              <text class="item-desc">在首页显示实时监测数据</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.display.showRealtimeData" 
                @change="toggleShowRealtimeData" 
                color="#3cc51f" />
            </view>
          </view>
        </view>
      </view>

      <!-- 数据管理 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">🗂️</text>
          <text class="section-title">数据管理</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item action-item">
            <view class="item-info">
              <text class="item-title">导出数据</text>
              <text class="item-desc">导出所有监测数据</text>
            </view>
            <view class="item-control">
              <text class="action-btn primary" @tap="exportAllData">导出</text>
            </view>
          </view>
          
          <view class="setting-item action-item">
            <view class="item-info">
              <text class="item-title">清除历史数据</text>
              <text class="item-desc">清除所有历史监测记录</text>
            </view>
            <view class="item-control">
              <text class="action-btn danger" @tap="clearHistoryData">清除</text>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">数据存储大小</text>
              <text class="item-desc">当前数据占用的存储空间</text>
            </view>
            <view class="item-control">
              <text class="storage-size">{{ formatStorageSize(storageUsed) }}</text>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">最大存储记录数</text>
              <text class="item-desc">保留的最大历史记录数量</text>
            </view>
            <view class="item-control">
              <input 
                class="text-input" 
                type="number" 
                v-model="settings.data.maxRecords"
                placeholder="1000" />
            </view>
          </view>
        </view>
      </view>

      <!-- 设备信息 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">📱</text>
          <text class="section-title">设备信息</text>
        </view>
        
        <view class="setting-items">
          <view class="info-item">
            <text class="info-label">设备型号</text>
            <text class="info-value">LYLS-00021</text>
          </view>
          <view class="info-item">
            <text class="info-label">固件版本</text>
            <text class="info-value">v1.0.0</text>
          </view>
          <view class="info-item">
            <text class="info-label">设备序列号</text>
            <text class="info-value">{{ deviceState.deviceInfo.serialNumber || 'LYLS202400001' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">IMEI</text>
            <text class="info-value">{{ deviceState.deviceInfo.imei || '未获取' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">SIM卡ICCID</text>
            <text class="info-value">{{ deviceState.deviceInfo.iccid || '未获取' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">电池电压</text>
            <text class="info-value">{{ deviceState.battery.voltage.toFixed(2) }}V</text>
          </view>
          <view class="info-item">
            <text class="info-label">电池电量</text>
            <text class="info-value">{{ deviceState.battery.level.toFixed(1) }}%</text>
          </view>
          <view class="info-item">
            <text class="info-label">充电状态</text>
            <text class="info-value">{{ deviceState.battery.charging ? '充电中' : '未充电' }}</text>
          </view>
        </view>
      </view>

      <!-- 关于和帮助 */
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">ℹ️</text>
          <text class="section-title">关于和帮助</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item action-item" @tap="showAppInfo">
            <view class="item-info">
              <text class="item-title">应用信息</text>
              <text class="item-desc">查看应用版本和更新日志</text>
            </view>
            <view class="item-control">
              <text class="nav-arrow">></text>
            </view>
          </view>
          
          <view class="setting-item action-item" @tap="showUserManual">
            <view class="item-info">
              <text class="item-title">使用手册</text>
              <text class="item-desc">查看详细的使用说明</text>
            </view>
            <view class="item-control">
              <text class="nav-arrow">></text>
            </view>
          </view>
          
          <view class="setting-item action-item" @tap="showPrivacyPolicy">
            <view class="item-info">
              <text class="item-title">隐私政策</text>
              <text class="item-desc">查看隐私保护政策</text>
            </view>
            <view class="item-control">
              <text class="nav-arrow">></text>
            </view>
          </view>
          
          <view class="setting-item action-item" @tap="contactSupport">
            <view class="item-info">
              <text class="item-title">技术支持</text>
              <text class="item-desc">联系技术支持团队</text>
            </view>
            <view class="item-control">
              <text class="nav-arrow">></text>
            </view>
          </view>
          
          <view class="setting-item action-item" @tap="checkUpdate">
            <view class="item-info">
              <text class="item-title">检查更新</text>
              <text class="item-desc">检查应用和固件更新</text>
            </view>
            <view class="item-control">
              <text class="action-btn primary">检查</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 版权信息 -->
    <view class="footer-info">
      <text class="copyright">© 2024 智能辐射检测系统</text>
      <text class="version">版本 1.0.0</text>
    </view>
    </scroll-view>
  </view>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { radiationState, deviceState } from '../../utils/dataStore.js'
import dataStore from '../../utils/dataStore.js'
import mqttService from '../../utils/mqttService.js'
import ToastContainer from '../../components/ToastContainer.vue'
import toastManager from '../../utils/toastManager.js'

export default {
  name: 'Settings',
  components: {
    ToastContainer
  },
  setup() {
    const mqttTestStatus = ref('测试连接')
    const storageUsed = ref(0)
    
    // 设置数据
    const settings = reactive({
      radiation: {
        maxDoseRate: radiationState.settings.maxDoseRate,
        minDoseRate: radiationState.settings.minDoseRate,
        maxDoseSum: radiationState.settings.maxDoseSum,
        autoUpload: radiationState.settings.autoUpload,
        uploadInterval: radiationState.settings.uploadInterval
      },
      mqtt: {
        host: 'broker.emqx.io',
        port: 8083,
        username: '',
        password: ''
      },
      notifications: {
        soundAlert: radiationState.settings.soundAlert,
        vibrationAlert: radiationState.settings.vibrationAlert,
        healthReminder: true,
        reminderInterval: 60,
        dataAnomalyAlert: true
      },
      display: {
        theme: 'dark',
        fontSize: 'medium',
        keepScreenOn: false,
        showRealtimeData: true
      },
      data: {
        maxRecords: 1000,
        autoBackup: false
      }
    })

    // 选择器选项
    const uploadIntervals = ['30秒', '1分钟', '5分钟', '10分钟', '30分钟', '1小时']
    const reminderIntervals = ['30分钟', '1小时', '2小时', '6小时', '12小时', '24小时']
    const themes = ['深色模式', '浅色模式', '自动切换']
    const fontSizes = ['小', '中', '大', '特大']

    // 选择器索引
    const uploadIntervalIndex = ref(2) // 默认5分钟
    const reminderIntervalIndex = ref(1) // 默认1小时
    const themeIndex = ref(0) // 默认深色模式
    const fontSizeIndex = ref(1) // 默认中等字体

    // 计算存储使用量
    const calculateStorageUsed = () => {
      try {
        const data = dataStore.exportData()
        const jsonStr = JSON.stringify(data)
        storageUsed.value = new Blob([jsonStr]).size
      } catch (error) {
        storageUsed.value = 0
      }
    }

    // 方法
    const toggleAutoUpload = (e) => {
      settings.radiation.autoUpload = e.detail.value
    }

    const toggleSoundAlert = (e) => {
      settings.notifications.soundAlert = e.detail.value
    }

    const toggleVibrationAlert = (e) => {
      settings.notifications.vibrationAlert = e.detail.value
    }

    const toggleHealthReminder = (e) => {
      settings.notifications.healthReminder = e.detail.value
    }

    const toggleKeepScreenOn = (e) => {
      settings.display.keepScreenOn = e.detail.value
      
      if (e.detail.value) {
        uni.setKeepScreenOn({
          keepScreenOn: true
        })
      } else {
        uni.setKeepScreenOn({
          keepScreenOn: false
        })
      }
    }

    const toggleShowRealtimeData = (e) => {
      settings.display.showRealtimeData = e.detail.value
    }

    const toggleDataAnomalyAlert = (e) => {
      settings.notifications.dataAnomalyAlert = e.detail.value
    }

    const onUploadIntervalChange = (e) => {
      uploadIntervalIndex.value = e.detail.value
      const intervals = [30, 60, 300, 600, 1800, 3600] // 对应秒数
      settings.radiation.uploadInterval = intervals[e.detail.value]
    }

    const onReminderIntervalChange = (e) => {
      reminderIntervalIndex.value = e.detail.value
      const intervals = [30, 60, 120, 360, 720, 1440] // 对应分钟数
      settings.notifications.reminderInterval = intervals[e.detail.value]
    }

    const onThemeChange = (e) => {
      themeIndex.value = e.detail.value
      const themeValues = ['dark', 'light', 'auto']
      settings.display.theme = themeValues[e.detail.value]
      
      toastManager.success('主题已切换')
    }

    const onFontSizeChange = (e) => {
      fontSizeIndex.value = e.detail.value
      const fontSizeValues = ['small', 'medium', 'large', 'extra-large']
      settings.display.fontSize = fontSizeValues[e.detail.value]
      
      toastManager.success('字体大小已调整')
    }

    const testMqttConnection = async () => {
      mqttTestStatus.value = '连接中...'
      
      try {
        // 使用设置中的MQTT配置进行连接测试
        const testConnection = new Promise((resolve, reject) => {
          const testService = {
            connect: (options) => {
              // 模拟连接测试
              setTimeout(() => {
                if (options.host && options.port) {
                  resolve('连接成功')
                } else {
                  reject('连接失败')
                }
              }, 2000)
            }
          }
          
          testService.connect({
            host: settings.mqtt.host,
            port: settings.mqtt.port,
            username: settings.mqtt.username,
            password: settings.mqtt.password
          })
        })
        
        const result = await testConnection
        mqttTestStatus.value = '连接成功 ✓'
        
        toastManager.success('MQTT连接成功')
        
        setTimeout(() => {
          mqttTestStatus.value = '测试连接'
        }, 3000)
        
      } catch (error) {
        mqttTestStatus.value = '连接失败 ✗'
        
        toastManager.error('MQTT连接失败')
        
        setTimeout(() => {
          mqttTestStatus.value = '测试连接'
        }, 3000)
      }
    }

    const exportAllData = () => {
      uni.showModal({
        title: '导出数据',
        content: '是否导出所有监测数据？这可能需要一些时间。',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '导出中...'
            })
            
            setTimeout(() => {
              try {
                const data = dataStore.exportData()
                const jsonStr = JSON.stringify(data, null, 2)
                
                uni.hideLoading()
                uni.showModal({
                  title: '导出成功',
                  content: `数据已准备完成，大小：${formatStorageSize(new Blob([jsonStr]).size)}`,
                  confirmText: '确定',
                  success: () => {
                    toastManager.success('数据已导出')
                  }
                })
              } catch (error) {
                uni.hideLoading()
                toastManager.error('导出失败')
              }
            }, 2000)
          }
        }
      })
    }

    const clearHistoryData = () => {
      uni.showModal({
        title: '清除数据',
        content: '确定要清除所有历史数据吗？此操作不可恢复！',
        confirmText: '确定清除',
        confirmColor: '#dc3545',
        success: (res) => {
          if (res.confirm) {
            dataStore.clearHistory()
            calculateStorageUsed()
            
            toastManager.success('数据已清除')
          }
        }
      })
    }

    const formatStorageSize = (bytes) => {
      if (bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const saveAllSettings = () => {
      // 保存辐射设置
      Object.assign(radiationState.settings, settings.radiation)
      
      // 保存MQTT设置
      if (settings.mqtt.host && settings.mqtt.port) {
        mqttService.disconnect()
        mqttService.connect({
          host: settings.mqtt.host,
          port: settings.mqtt.port,
          username: settings.mqtt.username,
          password: settings.mqtt.password
        })
      }
      
      // 保存到本地存储
      try {
        uni.setStorageSync('appSettings', JSON.stringify(settings))
        
        toastManager.success('设置已保存')
      } catch (error) {
        toastManager.error('保存失败')
      }
    }

    const resetAllSettings = () => {
      uni.showModal({
        title: '重置设置',
        content: '确定要重置所有设置为默认值吗？',
        success: (res) => {
          if (res.confirm) {
            // 重置为默认值
            settings.radiation.maxDoseRate = 1.0
            settings.radiation.minDoseRate = 0.01
            settings.radiation.maxDoseSum = 100.0
            settings.radiation.autoUpload = true
            settings.radiation.uploadInterval = 300
            
            settings.mqtt.host = 'broker.emqx.io'
            settings.mqtt.port = 8083
            settings.mqtt.username = ''
            settings.mqtt.password = ''
            
            settings.notifications.soundAlert = true
            settings.notifications.vibrationAlert = true
            settings.notifications.healthReminder = true
            settings.notifications.reminderInterval = 60
            settings.notifications.dataAnomalyAlert = true
            
            settings.display.theme = 'dark'
            settings.display.fontSize = 'medium'
            settings.display.keepScreenOn = false
            settings.display.showRealtimeData = true
            
            settings.data.maxRecords = 1000
            
            // 重置选择器索引
            uploadIntervalIndex.value = 2
            reminderIntervalIndex.value = 1
            themeIndex.value = 0
            fontSizeIndex.value = 1
            
            toastManager.success('设置已重置')
          }
        }
      })
    }

    const showAppInfo = () => {
      uni.showModal({
        title: '应用信息',
        content: '智能辐射检测系统 v1.0.0\n\n这是一款专业的辐射监测应用，提供实时监测、数据分析、健康评估等功能。\n\n开发团队：LYLS科技\n发布日期：2024年1月',
        confirmText: '确定',
        showCancel: false
      })
    }

    const showUserManual = () => {
      uni.showModal({
        title: '使用手册',
        content: '请访问官网查看详细的使用手册和技术文档。\n\n官网：www.lyls-tech.com\n技术支持：<EMAIL>',
        confirmText: '确定',
        showCancel: false
      })
    }

    const showPrivacyPolicy = () => {
      uni.showModal({
        title: '隐私政策',
        content: '我们重视您的隐私保护。所有监测数据均在本地存储，仅在您授权的情况下上传到服务器。\n\n详细的隐私政策请访问官网查看。',
        confirmText: '确定',
        showCancel: false
      })
    }

    const contactSupport = () => {
      uni.showActionSheet({
        itemList: ['发送邮件', '拨打电话', '在线客服'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              toastManager.info('邮件客户端已打开')
              break
            case 1:
              uni.makePhoneCall({
                phoneNumber: '************'
              })
              break
            case 2:
              toastManager.info('在线客服功能开发中')
              break
          }
        }
      })
    }

    const checkUpdate = () => {
      uni.showLoading({
        title: '检查中...'
      })
      
      setTimeout(() => {
        uni.hideLoading()
        uni.showModal({
          title: '检查更新',
          content: '当前已是最新版本。\n\n应用版本：v1.0.0\n固件版本：v1.0.0',
          confirmText: '确定',
          showCancel: false
        })
      }, 2000)
    }

    const resetDoseSum = () => {
      uni.showModal({
        title: '重置累积剂量',
        content: `确定要将累积剂量从 ${radiationState.currentData.doseSum.toFixed(6)} μSv 重置为 0 吗？`,
        confirmText: '确定重置',
        confirmColor: '#dc3545',
        success: (res) => {
          if (res.confirm) {
            try {
              // 调用MQTT服务的重置方法
              mqttService.resetDoseSum()
              
              // 直接更新状态
              radiationState.currentData.doseSum = 0
              
              toastManager.success('累积剂量已重置')
            } catch (error) {
              console.error('重置累积剂量失败:', error)
              toastManager.error('重置失败')
            }
          }
        }
      })
    }

    // 生命周期
    onMounted(() => {
      // 加载保存的设置
      try {
        const savedSettings = uni.getStorageSync('appSettings')
        if (savedSettings) {
          const parsed = JSON.parse(savedSettings)
          Object.assign(settings, parsed)
        }
      } catch (error) {
        console.error('加载设置失败:', error)
      }
      
      // 计算存储使用量
      calculateStorageUsed()
    })

    return {
      settings,
      deviceState,
      radiationState,
      mqttTestStatus,
      storageUsed,
      uploadIntervals,
      reminderIntervals,
      themes,
      fontSizes,
      uploadIntervalIndex,
      reminderIntervalIndex,
      themeIndex,
      fontSizeIndex,
      toggleAutoUpload,
      toggleSoundAlert,
      toggleVibrationAlert,
      toggleHealthReminder,
      toggleKeepScreenOn,
      toggleShowRealtimeData,
      toggleDataAnomalyAlert,
      onUploadIntervalChange,
      onReminderIntervalChange,
      onThemeChange,
      onFontSizeChange,
      testMqttConnection,
      exportAllData,
      clearHistoryData,
      formatStorageSize,
      saveAllSettings,
      resetAllSettings,
      resetDoseSum,
      showAppInfo,
      showUserManual,
      showPrivacyPolicy,
      contactSupport,
      checkUpdate
    }
  }
}
</script>

<style scoped>
.settings-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  padding: 24rpx;
  position: relative;
}

.settings-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 40% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(100, 116, 139, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* 头部信息 - 现代浅色设计 */
.settings-header {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8rpx 32rpx rgba(15, 23, 42, 0.08);
  overflow: hidden;
  animation: slideInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #8b5cf6, #7c3aed, #6d28d9);
  border-radius: 32rpx 32rpx 0 0;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background: linear-gradient(90deg, #8b5cf6, #7c3aed, #6d28d9); }
  50% { background: linear-gradient(90deg, #6d28d9, #7c3aed, #8b5cf6); }
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.header-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-icon {
  font-size: 48rpx;
  animation: iconRotate 3s ease-in-out infinite;
}

.icon-glow {
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.2) 0%, transparent 70%);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes iconRotate {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(10deg); }
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.header-title {
  font-size: 36rpx;
  color: #0f172a;
  font-weight: 800;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-subtitle {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 16rpx;
  color: #64748b;
  font-size: 20rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn:hover {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: #ffffff;
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(139, 92, 246, 0.3);
  transform: translateY(-2rpx);
}

.action-btn.save {
  background: linear-gradient(135deg, #10b981, #059669);
  color: #ffffff;
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);
}

.action-btn.save:hover {
  background: linear-gradient(135deg, #059669, #047857);
  box-shadow: 0 6rpx 16rpx rgba(16, 185, 129, 0.4);
}

.btn-icon {
  font-size: 18rpx;
}

.btn-text {
  font-size: 20rpx;
  font-weight: 600;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.profile-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 48rpx;
  font-weight: 700;
  box-shadow: 0 8rpx 25rpx rgba(139, 92, 246, 0.3);
  position: relative;
  overflow: hidden;
}

.profile-avatar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.1) 50%, transparent 60%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes slideInDown {
  from { opacity: 0; transform: translateY(-30rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInUp {
  from { opacity: 0; transform: translateY(30rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-30rpx); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(30rpx); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.3); }
  50% { opacity: 1; transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.05); }
}

.profile-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  color: #0f172a;
  font-weight: 800;
  margin-bottom: 8rpx;
  background: linear-gradient(135deg, #334155, #1e293b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.user-email {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.membership-badge {
  display: inline-block;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: #ffffff;
  font-size: 18rpx;
  font-weight: 600;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(245, 158, 11, 0.3);
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(203, 213, 225, 0.8);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.08);
}

.stat-value {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 6rpx;
  text-shadow: 0 2rpx 4rpx rgba(15, 23, 42, 0.08);
}

.stat-label {
  font-size: 18rpx;
  color: #64748b;
  font-weight: 500;
}

/* 设置分组 - 优雅设计 */
.settings-sections {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.settings-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.settings-section:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(15, 23, 42, 0.12);
}

.settings-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  border-radius: 24rpx 24rpx 0 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.settings-section.general::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.settings-section.device::before {
  background: linear-gradient(90deg, #00b4d8, #0096c7);
}

.settings-section.notification::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.settings-section.privacy::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.settings-section.about::before {
  background: linear-gradient(90deg, #64748b, #475569);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 28rpx;
}

.section-icon {
  font-size: 28rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.section-title {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
}

.section-desc {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  margin-left: auto;
}

/* 设置项 - 现代设计 */
.setting-items {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.setting-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(203, 213, 225, 0.8);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.08);
}

.setting-item:active {
  transform: translateY(-1rpx) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(15, 23, 42, 0.06);
}

.setting-main {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.setting-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.setting-item:hover .setting-icon {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(203, 213, 225, 0.8);
  transform: scale(1.05);
}

.setting-content {
  flex: 1;
}

.setting-title {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 4rpx;
}

.setting-desc {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.3;
}

.setting-value {
  font-size: 22rpx;
  color: #475569;
  font-weight: 600;
  margin-right: 12rpx;
}

.setting-control {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.setting-arrow {
  font-size: 20rpx;
  color: #94a3b8;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.setting-item:hover .setting-arrow {
  color: #64748b;
  transform: translateX(4rpx);
}

/* 开关控件 - 优雅设计 */
.switch-control {
  width: 80rpx;
  height: 44rpx;
  background: rgba(148, 163, 184, 0.3);
  border-radius: 22rpx;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.switch-control.active {
  background: linear-gradient(135deg, #10b981, #059669);
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.2);
}

.switch-knob {
  width: 36rpx;
  height: 36rpx;
  background: #ffffff;
  border-radius: 50%;
  position: absolute;
  top: 3rpx;
  left: 3rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.switch-control.active .switch-knob {
  left: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.2);
}

/* 滑块控件 - 现代设计 */
.slider-control {
  width: 200rpx;
  height: 8rpx;
  background: rgba(226, 232, 240, 0.8);
  border-radius: 4rpx;
  position: relative;
  cursor: pointer;
}

.slider-track {
  height: 100%;
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
  border-radius: 4rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slider-thumb {
  width: 24rpx;
  height: 24rpx;
  background: #ffffff;
  border: 3rpx solid #8b5cf6;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.2);
  cursor: pointer;
}

.slider-thumb:hover {
  transform: translateY(-50%) scale(1.2);
  box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.3);
}

/* 选择器控件 - 优雅设计 */
.selector-control {
  position: relative;
}

.selector-trigger {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 12rpx;
  min-width: 150rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.selector-trigger:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(203, 213, 225, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(15, 23, 42, 0.04);
}

.selector-text {
  font-size: 20rpx;
  color: #475569;
  font-weight: 500;
  flex: 1;
}

.selector-arrow {
  font-size: 16rpx;
  color: #94a3b8;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.selector-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(15, 23, 42, 0.15);
  z-index: 100;
  margin-top: 8rpx;
  overflow: hidden;
  animation: dropdownShow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dropdownShow {
  from {
    opacity: 0;
    transform: translateY(-8rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdown-option {
  padding: 16rpx 20rpx;
  font-size: 20rpx;
  color: #475569;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid rgba(226, 232, 240, 0.4);
}

.dropdown-option:last-child {
  border-bottom: none;
}

.dropdown-option:hover {
  background: rgba(248, 250, 252, 0.8);
  color: #0f172a;
}

.dropdown-option.active {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: #ffffff;
}

/* 危险操作区域 - 特殊设计 */
.danger-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1px solid rgba(239, 68, 68, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.06);
  position: relative;
  overflow: hidden;
}

.danger-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #ef4444, #dc2626);
  border-radius: 24rpx 24rpx 0 0;
}

.danger-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 28rpx;
}

.danger-icon {
  font-size: 28rpx;
  color: #ef4444;
  filter: drop-shadow(0 2rpx 4rpx rgba(239, 68, 68, 0.2));
}

.danger-title {
  font-size: 28rpx;
  color: #dc2626;
  font-weight: 700;
}

.danger-items {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.danger-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: rgba(254, 242, 242, 0.6);
  border-radius: 16rpx;
  border: 1px solid rgba(239, 68, 68, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.danger-item:hover {
  background: rgba(254, 242, 242, 0.8);
  border-color: rgba(239, 68, 68, 0.2);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.08);
}

.danger-content {
  flex: 1;
}

.danger-item-title {
  font-size: 24rpx;
  color: #dc2626;
  font-weight: 700;
  margin-bottom: 4rpx;
}

.danger-item-desc {
  font-size: 20rpx;
  color: #991b1b;
  font-weight: 500;
  line-height: 1.3;
}

.danger-button {
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: #ffffff;
  font-size: 18rpx;
  font-weight: 600;
  border-radius: 12rpx;
  border: 1px solid rgba(239, 68, 68, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.2);
  cursor: pointer;
}

.danger-button:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(239, 68, 68, 0.3);
}

/* 版本信息 - 精致设计 */
.version-info {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-top: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(15, 23, 42, 0.06);
  text-align: center;
  overflow: hidden;
}

.version-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #94a3b8, #64748b);
  border-radius: 24rpx 24rpx 0 0;
}

.app-logo {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 20rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 700;
  box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.3);
}

.app-name {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.app-version {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.version-details {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  margin-bottom: 24rpx;
}

.version-item {
  text-align: center;
}

.version-label {
  display: block;
  font-size: 16rpx;
  color: #94a3b8;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.version-value {
  font-size: 20rpx;
  color: #475569;
  font-weight: 600;
}

.copyright {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
  line-height: 1.4;
}

/* 响应式设计优化 */
@media (max-width: 750rpx) {
  .settings-container {
    padding: 16rpx;
  }
  
  .settings-header {
    padding: 32rpx 24rpx;
  }
  
  .user-profile {
    flex-direction: column;
    text-align: center;
    gap: 16rpx;
  }
  
  .profile-avatar {
    width: 100rpx;
    height: 100rpx;
    font-size: 40rpx;
  }
  
  .stats-overview {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: 16rpx;
  }
  
  .setting-main {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12rpx;
  }
  
  .setting-control {
    justify-content: center;
  }
  
  .selector-dropdown {
    left: 0;
    right: 0;
    min-width: auto;
  }
  
  .version-details {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .danger-item {
    flex-direction: column;
    align-items: stretch;
    gap: 16rpx;
  }
  
  .danger-content {
    text-align: center;
  }
  
  .user-name {
    font-size: 28rpx;
  }
  
  .section-title {
    font-size: 24rpx;
  }
}

/* 弹窗遮罩 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.4);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24rpx;
  padding: 40rpx;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 20rpx 60rpx rgba(15, 23, 42, 0.2);
  max-width: 500rpx;
    width: 100%;
  animation: modalShow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalShow {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.modal-title {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 12rpx;
}

.modal-desc {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.4;
}

.modal-actions {
  display: flex;
  gap: 16rpx;
  justify-content: center;
}

.modal-btn {
  padding: 16rpx 32rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid;
  }
  
.modal-btn.primary {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: #ffffff;
  border-color: rgba(239, 68, 68, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.3);
}

.modal-btn.secondary {
  background: rgba(248, 250, 252, 0.8);
  color: #64748b;
  border-color: rgba(226, 232, 240, 0.6);
}

.modal-btn:active {
  transform: scale(0.98);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx 40rpx;
  color: #94a3b8;
}

.loading-spinner {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid rgba(139, 92, 246, 0.2);
  border-top: 4rpx solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
}

/* 成功状态 */
.success-state {
  text-align: center;
  padding: 60rpx 40rpx;
  color: #059669;
}

.success-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(16, 185, 129, 0.2));
}

.success-title {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 12rpx;
}

.success-desc {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.4;
}

/* 特殊动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(20rpx); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

.fade-in {
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up {
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 提示气泡 */
.tooltip {
  position: absolute;
  background: rgba(15, 23, 42, 0.9);
  color: #ffffff;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 16rpx;
  font-weight: 500;
  white-space: nowrap;
  z-index: 200;
  pointer-events: none;
  animation: tooltipShow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6rpx solid transparent;
  border-top-color: rgba(15, 23, 42, 0.9);
}

@keyframes tooltipShow {
  from {
    opacity: 0;
    transform: translateY(-4rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 